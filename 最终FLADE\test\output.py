import pandas as pd
from datetime import datetime
import openpyxl
from openpyxl.styles import Font, PatternFill
from openpyxl.styles.borders import Border, Side
import numpy as np
from collections import defaultdict
import os  # Add this import statement

def export_replacement_data_to_excel(replacement_data, network_name, k, p, kurtosis_stats=None):
    """
    将节点替换过程的详细信息导出到Excel文件
    
    参数:
    replacement_data: 包含节点替换信息的列表
    network_name: 网络名称
    k: 种子集大小
    p: 传播概率
    kurtosis_stats: 峰度统计数据，格式为(iteration, kurtosis_value)的列表
    """
    if not replacement_data:
        print("没有节点替换数据可供导出")
        return
    
    # 将kurtosis_stats转换为字典，便于查找
    kurtosis_dict = {}
    if kurtosis_stats:
        kurtosis_dict = {item[0]: item[1] for item in kurtosis_stats}
        
    # 准备数据
    data = []
    for rep_info in replacement_data:
        # 计算替换前后的适应度变化
        fitness_change = rep_info['new_fitness'] - rep_info['old_fitness']  # 修改为使用new_fitness
        improvement = "是" if fitness_change > 0 else "否"
        fitness_change_percentage = (fitness_change / rep_info['old_fitness']) * 100 if rep_info['old_fitness'] != 0 else 0
        
        # 计算替换前后的节点差异
        old_set = set(rep_info['old_individual'])
        new_set = set(rep_info['new_individual'])
        removed_nodes = list(old_set - new_set)
        added_nodes = list(new_set - old_set)
        
        # 获取当前迭代的峰度值
        iteration = rep_info['iteration']
        kurtosis_value = kurtosis_dict.get(iteration, "N/A")
        
        # 判断是否超过全局最优
        # 由于我们需要检查节点替换后的适应度是否超过了当前全局最优
        # 这里假设具有exceeded_global_best字段的是超过了全局最优的
        exceeded_global_best = rep_info.get('exceeded_global_best', False)
        
        # 创建数据行
        row = {
            '迭代次数': rep_info['iteration'],
            '个体索引': rep_info['individual_idx'],
            '是否超过全局最优': '是' if exceeded_global_best else '否',
            '替换节点数量': rep_info['num_replaced'],
            '替换前适应度': rep_info['old_fitness'],
            '替换后适应度': rep_info['new_fitness'],  # 修改这里，使用'new_fitness'而不是'actual_new_fitness'
            '适应度变化': fitness_change,
            '适应度变化百分比': f"{fitness_change_percentage:.2f}%",
            '移除的节点': str(removed_nodes),
            '添加的节点': str(added_nodes),
            '是否改进': '是' if fitness_change > 0 else '否',
            '峰度值': kurtosis_value 
        }
        data.append(row)
    
    # 创建DataFrame
    df = pd.DataFrame(data)
    
    # 生成文件名和完整路径
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    network_name = network_name.split('\\')[-1].split('.')[0]  # 提取网络文件名
    output_dir = r"D:\VS\code\fitness_landscape\地形特征测量\峰度引导DE实验\局部引导信息输出"
    os.makedirs(output_dir, exist_ok=True)  # 确保目录存在
    filename = os.path.join(output_dir, f"局部引导信息{network_name}_k{k}_p{p}.xlsx")
    
    # 创建Excel写入器
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        # 写入数据
        df.to_excel(writer, index=False, sheet_name='节点替换数据')
        
        # 获取工作表
        worksheet = writer.sheets['节点替换数据']
        
        # 设置列宽
        for idx, col in enumerate(df.columns):
            max_length = max(
                df[col].astype(str).apply(len).max(),
                len(col)
            )
            worksheet.column_dimensions[chr(65 + idx)].width = min(max_length + 2, 50)
        
        # 设置单元格字体
        for row_idx, row in enumerate(df.iterrows(), start=2):  # start=2 因为第1行是标题
            # 如果是超过全局最优的行，添加蓝色字体
            if row[1]['是否超过全局最优'] == '是':
                for col in range(1, len(df.columns) + 1):
                    cell = worksheet.cell(row=row_idx, column=col)
                    cell.font = openpyxl.styles.Font(color='0000FF', bold=True)
            # 如果适应度有改进，添加红色字体
            elif row[1]['是否改进'] == '是':
                for col in range(1, len(df.columns) + 1):
                    cell = worksheet.cell(row=row_idx, column=col)
                    cell.font = openpyxl.styles.Font(color='FF0000', bold=True)
    
    # print(f"节点替换数据已导出到文件: {filename}")
    print(f"节点替换数据已导出到文件")

    
    # 计算并打印统计信息
    total_replacements = len(replacement_data)
    successful_replacements = sum(1 for row in data if row['是否改进'] == '是')
    success_rate = (successful_replacements / total_replacements) * 100
    
    return filename

def export_evolution_stats_to_excel(evolution_data, kurtosis_stats, replacement_data, network_path, k, p, max_iter):
    """
    将进化过程中每代的适应度变化和峰度数据导出到Excel文件
    所有数据放在一个sheet表中，行为进化次数，列为所有个体适应度和峰度
    
    参数:
    evolution_data: 进化过程数据
    kurtosis_stats: 峰度统计数据
    replacement_data: 节点替换数据
    network_path: 网络文件路径
    k: 种子集大小
    p: 传播概率
    max_iter: 最大迭代次数
    
    返回:
    excel_file: 保存的Excel文件路径
    """
    # 提取网络名称
    network_name = network_path.split('\\')[-1].split('.')[0]
    
    # 创建时间戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 创建Excel文件名
    excel_file = f"evolution_stats_{network_name}_k{k}_p{p}_{timestamp}.xlsx"
    
    # 获取种群大小
    population_size = len(evolution_data[0]['individuals']) if evolution_data else 0
    
    # 创建一个空的DataFrame，行为迭代次数，列为个体适应度和统计指标
    index = [f'第{i}代' for i in range(1, max_iter + 1)]
    columns = [f'个体{i+1}适应度' for i in range(population_size)]
    # 添加新的统计列
    columns.extend(['平均适应度', '最大适应度', '最小适应度', '适应度标准差', '峰度值', '地形', 
                   '引导个体数', '引导成功率', '引导提升幅度'])

    # 创建DataFrame
    df = pd.DataFrame(index=index, columns=columns)
    
    # 填充数据
    for iter_num in range(max_iter):
        if iter_num < len(evolution_data):
            data = evolution_data[iter_num]
            fitness_values = data.get('fitness', [])
            terrain = data.get('terrain', 'unknown')
            
            # 计算引导统计信息 - 注意这里iter_num+1是因为replacement_data中的iteration从1开始
            guided_count = 0
            guided_success = 0
            guided_improvement = 0.0
            
            # 当前迭代的引导记录
            current_iteration_replacements = [r for r in replacement_data if r['iteration'] == iter_num + 1]
            
            for record in current_iteration_replacements:
                guided_count += 1
                new_fitness = record.get('actual_new_fitness', record['new_fitness'])
                old_fitness = record['old_fitness']
                if new_fitness > old_fitness:
                    guided_success += 1
                    guided_improvement += (new_fitness - old_fitness)
            
            # 填充引导统计信息
            df.at[f'第{iter_num+1}代', '引导个体数'] = guided_count
            df.at[f'第{iter_num+1}代', '引导成功率'] = guided_success / guided_count if guided_count > 0 else 0
            df.at[f'第{iter_num+1}代', '引导提升幅度'] = guided_improvement / guided_success if guided_success > 0 else 0

            # 填充个体适应度
            for ind_idx in range(min(population_size, len(fitness_values))):
                df.at[f'第{iter_num+1}代', f'个体{ind_idx+1}适应度'] = fitness_values[ind_idx]
            
            # 填充统计数据（确保fitness_values非空）
            if fitness_values:
                df.at[f'第{iter_num+1}代', '平均适应度'] = np.mean(fitness_values)
                df.at[f'第{iter_num+1}代', '最大适应度'] = np.max(fitness_values)
                df.at[f'第{iter_num+1}代', '最小适应度'] = np.min(fitness_values)
                df.at[f'第{iter_num+1}代', '适应度标准差'] = np.std(fitness_values)
            df.at[f'第{iter_num+1}代', '地形'] = terrain
            
            # 获取当前代的峰度值
            kurtosis_value = next((kurt for i, kurt in kurtosis_stats if i == iter_num + 1), None)
            df.at[f'第{iter_num+1}代', '峰度值'] = kurtosis_value
    
    # 保存到Excel
    df.to_excel(excel_file)
    
    # 添加字体颜色标记
    red_font = Font(color='FF0000')  # 红色字体
    blue_font = Font(color='0000FF')  # 蓝色字体
    green_font = Font(color='00AA00')  # 绿色字体

    wb = openpyxl.load_workbook(excel_file)
    ws = wb.active

    # 添加颜色图例
    ws.insert_rows(1)
    ws.cell(row=1, column=1).value = "图例:"
    ws.cell(row=1, column=2).value = "最优个体"
    ws.cell(row=1, column=2).font = red_font
    ws.cell(row=1, column=3).value = "成功引导个体"
    ws.cell(row=1, column=3).font = green_font
    ws.cell(row=1, column=4).value = "被引导个体"
    ws.cell(row=1, column=4).font = blue_font
    
    # 给图例添加边框
    thin_border = Border(left=Side(style='thin'), right=Side(style='thin'), 
                         top=Side(style='thin'), bottom=Side(style='thin'))
    for col in range(1, 5):
        ws.cell(row=1, column=col).border = thin_border

    # 添加标记说明
    ws.insert_rows(2)
    ws.cell(row=2, column=1).value = "注意:"
    ws.cell(row=2, column=2).value = "若个体替换前后适应度相同，则不标记颜色"
    ws.merge_cells(start_row=2, start_column=2, end_row=2, end_column=5)

    # 创建逆向查找结构，从(iteration, individual_idx)映射到replacement_record
    replacement_lookup = {}
    for record in replacement_data:
        key = (record['iteration'], record['individual_idx'])
        replacement_lookup[key] = record

    # 遍历每一行标记颜色（从第3行开始，因为插入了图例行和说明行）
    for row_idx in range(3, ws.max_row + 1):
        # Excel行号转换为迭代次数（注意这里减2是因为我们插入了两行：图例和说明）
        iter_num = row_idx - 2
        
        # 跳过超出演化数据范围的行
        if iter_num > len(evolution_data):
            continue
            
        if iter_num <= len(evolution_data):
            # 获取当前代的数据
            generation_data = evolution_data[iter_num-1]
            fitness_values = generation_data.get('fitness', [])
            
            # 迭代次数从0开始，但replacement_data中iteration从1开始
            current_iter_for_replacement = iter_num
            
            # 标记所有个体颜色（一次遍历完成所有标记）
            for ind_idx in range(min(population_size, len(fitness_values))):
                # 获取对应的excel列
                col_idx = ind_idx + 2  # Excel从1开始，A列是索引列(1)，实际数据从列B(2)开始
                
                # 检查列索引有效性
                if 1 <= col_idx <= ws.max_column:
                    cell = ws.cell(row=row_idx, column=col_idx)
                    
                    # 首先检查此个体是否有替换记录
                    replacement_key = (current_iter_for_replacement, ind_idx)
                    if replacement_key in replacement_lookup:
                        record = replacement_lookup[replacement_key]
                        new_fitness = record.get('actual_new_fitness', record['new_fitness'])
                        old_fitness = record['old_fitness']
                        
                        # 只有在适应度有实际改进时才标记
                        if abs(new_fitness - old_fitness) > 1e-10:  # 不近似相等，有变化
                            if new_fitness > old_fitness:
                                # 成功引导，标记为绿色
                                cell.font = green_font
                            else:
                                # 失败引导，标记为蓝色
                                cell.font = blue_font
            
            # 最后标记最优个体为红色（覆盖其他颜色）
            if fitness_values:  # 确保fitness_values非空
                try:
                    best_idx = np.argmax(fitness_values)
                    best_col = best_idx + 2  # 调整为Excel列索引
                    if 1 <= best_col <= ws.max_column:  # 确保列索引有效
                        best_cell = ws.cell(row=row_idx, column=best_col)
                        best_cell.font = red_font
                except (ValueError, TypeError) as e:
                    print(f"标记第{iter_num}代最优个体时出错: {e}")

    wb.save(excel_file)
    print(f"已将进化统计数据保存到: {excel_file}")
    return excel_file

