import numpy as np
import networkx as nx
import random
import matplotlib.pyplot as plt
import math
from base_fun import IC, gen_graph, EDV, degree_initialization, local_search, visualize_seed_set, plot_fitness_history
import time
from collections import defaultdict
from evolution_trajectory import plot_evolution_trajectory, plot_fitness_landscape, plot_evolution_on_landscape

# 全局缓存，避免重复计算EDV
edv_cache = {}

# 包装EDV函数，使用全局缓存
def cached_edv(G, seed_set, p, k=None):
    """缓存版本的EDV，避免重复计算"""
    key = (tuple(sorted(seed_set)), p)
    if key not in edv_cache:
        edv_cache[key] = EDV(G, seed_set, p, k)
    return edv_cache[key]

# 计算两个解之间的汉明距离（用于度量解的差异性）
def calculate_distance(sol1, sol2):
    """计算两个解之间的汉明距离"""
    return len(set(sol1).symmetric_difference(set(sol2)))

# 优化的随机游走探测算法
def random_walk_measurement(G, population, nodelist, k, num_walks=50, p=0.1):
    # 使用集合加速查找
    nodelist_set = set(nodelist)
    
    # 预计算所有个体的适应度（使用缓存）
    unique_individuals = {tuple(sorted(ind)) for ind in population}
    population_fitness = {ind: cached_edv(G, list(ind), p) for ind in unique_individuals}
    
    delta_f = []
    # 处理标准随机游走
    for _ in range(num_walks):
        S = random.choice(population)
        S_tuple = tuple(sorted(S))
        f_S = population_fitness.get(S_tuple)
        if f_S is None:
            f_S = cached_edv(G, S, p)
            population_fitness[S_tuple] = f_S
        
        # 高效生成邻域解
        S_set = set(S)
        removed_node = random.choice(S)
        available_nodes = list(nodelist_set - S_set)
        if available_nodes:
            new_node = random.choice(available_nodes)
            S_prime_set = S_set.copy()
            S_prime_set.remove(removed_node)
            S_prime_set.add(new_node)
            S_prime = tuple(sorted(S_prime_set))
            
            f_S_prime = population_fitness.get(S_prime)
            if f_S_prime is None:
                f_S_prime = cached_edv(G, list(S_prime_set), p)
                population_fitness[S_prime] = f_S_prime
                
            delta_f.append(f_S_prime - f_S)
    
    # 多节点变异部分
    for _ in range(num_walks//4):
        S = random.choice(population)
        S_set = set(S)
        S_list = list(S_set)
        f_S = cached_edv(G, S, p)
        
        replace_num = min(random.randint(1, 3), len(S_list))
        nodes_to_remove = set(random.sample(S_list, replace_num))
        available = list(nodelist_set - S_set)
        
        if len(available) >= replace_num:
            nodes_to_add = set(random.sample(available, replace_num))
            S_prime_set = (S_set - nodes_to_remove) | nodes_to_add
            S_prime = list(S_prime_set)
            
            f_S_prime = cached_edv(G, S_prime, p)
            delta_f.append(f_S_prime - f_S)
    
    # 计算多样性
    if len(population) >= 10:
        sample_size = min(10, len(population))
        sample_indices = random.sample(range(len(population)), sample_size)
        sampled_pop = [population[i] for i in sample_indices]
        diversity = np.mean([calculate_distance(sampled_pop[i], sampled_pop[j]) 
                           for i in range(sample_size) 
                           for j in range(i+1, sample_size)])
    else:
        diversity = np.mean([calculate_distance(population[i], population[j]) 
                           for i in range(len(population)) 
                           for j in range(i+1, len(population))])
    
    # 判断地形
    improvement_count = sum(1 for df in delta_f if df > 0)
    non_improvement_count = len(delta_f) - improvement_count
    
    # 如果50%以上的邻域解没有提升，就判定为山峰地形
    if non_improvement_count >= len(delta_f) * 0.5:
        terrain = "peak"  # 大部分解没有提升，定义为峰值
    else:
        terrain = "plateau"  # 大部分解有提升，定义为高原区
    
    return terrain

# 优化的适应度距离相关性计算
def calculate_u(population, best_solution, G, p):
    """
    计算适应度距离相关性特征值u
    """
    if not population or len(population) == 1:
        return 0.5  # 无意义情况返回中间值
    
    # 使用全局缓存进行适应度计算
    dist_fitness_pairs = []
    best_solution_set = set(best_solution)
    
    for ind in population:
        # 使用集合操作计算距离，更高效
        distance = len(set(ind).symmetric_difference(best_solution_set))
        fitness = cached_edv(G, ind, p)
        dist_fitness_pairs.append((distance, fitness))
    
    # 按距离排序
    dist_fitness_pairs.sort(key=lambda x: x[0])
    
    # 检查距离增加时适应度是否减少
    h = 0
    prev_fitness = None
    for _, fitness in dist_fitness_pairs:
        if prev_fitness is not None and fitness < prev_fitness:
            h += 1
        prev_fitness = fitness
    
    # 归一化
    u = h / (len(population) - 1)
    return max(0.0, min(u, 1.0))  # 确保u∈[0,1]

# 优化的差分变异操作
def Differential_Mutation(G, X, F, pop, k, nodelist, p=0.1, terrain="ridge", u=None, best_solution=None):
    """
    优化版差分变异操作
    """
    # 使用类属性来保持节点评分的持久性
    if not hasattr(Differential_Mutation, 'node_scores') or random.random() < 0.1:
        # 计算并存储所有节点的影响力评分
        Differential_Mutation.node_scores = {}
        for node in G.nodes():
            score = cached_edv(G, [node], p)
            Differential_Mutation.node_scores[node] = score
    
    node_scores = Differential_Mutation.node_scores
    nodes_set = set(G.nodes())
    M = []  # 变异种群结果容器
    
    # 检查种群多样性
    unique_solutions = {tuple(sorted(X[i])) for i in range(pop)}
    low_diversity = len(unique_solutions) <= 1
    
    # 自适应搜索模式设置
    if u is not None and best_solution is not None:
        adaptive_mode = True
    else:
        adaptive_mode = False
    
    # 预处理X，使用集合表示，加速后续操作
    X_sets = [set(x) for x in X]
    
    # 对每个个体执行变异操作
    for i in range(pop):
        # 当前解
        Xcurrent = X[i].copy()
        
        # === 基于适应性参数u的搜索策略选择 ===
        if adaptive_mode:
            use_global = random.random() < (1 - u)  # 全局搜索概率=1-u
            
            if use_global:
                rand_indices = random.sample(range(pop), 3)
                Xr1 = X[rand_indices[0]].copy()  # 基向量
                Xr2 = X[rand_indices[1]].copy()  # 差分向量1的起点
                Xr3 = X[rand_indices[2]].copy()  # 差分向量1的终点
                
                M.append(Xr1)  # 以Xr1为基础进行变异
                # 差分操作+9：使用集合操作加速
                diff_set = X_sets[rand_indices[1]] - X_sets[rand_indices[2]]
            else:
                # 局部搜索策略: DE/best/2/bin
                Xr1 = best_solution.copy()  # 使用全局最优解作为基向量
                
                rand_indices = random.sample(range(pop), 2)
                Xr2 = X[rand_indices[0]].copy()
                Xr3 = X[rand_indices[1]].copy()
                
                M.append(Xr1)  # 以全局最优解为基础进行变异
                
                # 使用集合操作加速差分计算
                best_set = set(best_solution)
                diff_set1 = best_set - X_sets[rand_indices[0]]
                diff_set2 = best_set - X_sets[rand_indices[1]]
                diff_set = diff_set1.union(diff_set2)
        
        # === 基于地形特征的搜索策略选择 ===
        elif terrain == "peak":
            rand_indices = random.sample(range(pop), min(3, pop))
            Xr1 = X[rand_indices[0]].copy()
            Xr2 = X[rand_indices[1]].copy()
            Xr3 = X[rand_indices[2]].copy()
            
            M.append(Xr1)
            diff_set = X_sets[rand_indices[1]] - X_sets[rand_indices[2]]
            
        elif terrain == "ridge":
            rand_indices = random.sample(range(pop), 5)
            Xr1 = X[rand_indices[0]].copy()
            Xr2 = X[rand_indices[1]].copy()
            Xr3 = X[rand_indices[2]].copy()
            Xr4 = X[rand_indices[3]].copy()
            Xr5 = X[rand_indices[4]].copy()
            
            M.append(Xr1)
            diff_set1 = X_sets[rand_indices[1]] - X_sets[rand_indices[2]]
            diff_set2 = X_sets[rand_indices[3]] - X_sets[rand_indices[4]]
            diff_set = diff_set1.union(diff_set2)
            
        else:  # "plateau" 高原区
            rand_indices = random.sample(range(pop), min(3, pop))
            Xr1 = X[rand_indices[0]].copy()
            Xr2 = X[rand_indices[1]].copy()
            Xr3 = X[rand_indices[2]].copy()
            
            M.append(Xcurrent)
            diff_set1 = X_sets[rand_indices[0]] - set(Xcurrent)
            diff_set2 = X_sets[rand_indices[1]] - X_sets[rand_indices[2]]
            diff_set = diff_set1.union(diff_set2)
        
        # === 执行变异操作 ===
        N = math.ceil(F * len(diff_set))
        
        # 当前解的节点集合
        current_set = set(M[i])
        
        # 对当前解评分，找出最差节点进行替换
        for _ in range(N):
            if not current_set:
                break
                
            # 基于节点评分识别当前解中的最差节点
            worst_node = min(current_set, key=lambda node: node_scores.get(node, 0))
            
            # 优先从差分向量选择节点
            available_diff = list(diff_set - current_set)
            
            if available_diff:
                replace_node = random.choice(available_diff)
            else:
                available_nodes = list(nodes_set - current_set)
                if not available_nodes:
                    continue
                replace_node = random.choice(available_nodes)
            
            # 执行替换
            try:
                worst_index = M[i].index(worst_node)
                M[i][worst_index] = replace_node
                current_set.remove(worst_node)
                current_set.add(replace_node)
            except ValueError:
                # 处理索引错误，确保程序不会崩溃
                if worst_node in current_set:
                    current_set.remove(worst_node)
                if replace_node not in current_set:
                    current_set.add(replace_node)
                # 重建M[i]以确保一致性
                M[i] = list(current_set)
                while len(M[i]) < k:
                    candidate = random.choice(list(nodes_set - current_set))
                    M[i].append(candidate)
                    current_set.add(candidate)
            
    return M

# 优化的交叉操作
def Crossover(X, M, cr, pop, k, nodelist):
    """
    优化版交叉操作
    """
    nodelist_set = set(nodelist)  # 预先创建节点集合以加速操作
    real_C = []
    
    for i in range(pop):
        # 创建跟踪集合和结果列表
        added_nodes = set()
        C = []
        
        for j in range(k):
            ran = random.random()
            
            # 使用位运算优化逻辑判断
            if (ran < cr and M[i][j] not in added_nodes) or \
               (ran < cr and M[i][j] in added_nodes and X[i][j] not in added_nodes) or \
               (ran >= cr and X[i][j] in added_nodes and M[i][j] not in added_nodes):
                temp = M[i][j] if M[i][j] not in added_nodes else X[i][j]
            else:
                temp = X[i][j]
            
            # 解决冲突
            if temp in added_nodes:
                # 预先过滤可用节点，避免重复检查
                available_nodes = list(nodelist_set - added_nodes)
                if available_nodes:
                    temp = random.choice(available_nodes)
            
            C.append(temp)
            added_nodes.add(temp)
        
        # 高效补充缺失节点
        if len(C) < k:
            available_nodes = list(nodelist_set - added_nodes)
            # 一次性添加所有缺失节点
            C.extend(random.sample(available_nodes, min(k - len(C), len(available_nodes))))
            
        real_C.append(C)
    
    return real_C

# 优化的选择操作
def Selection(G, X, C, pop, p=0.1):
    """
    优化版选择操作，使用缓存加速适应度计算
    """
    # 创建元组标识以便使用字典缓存
    X_tuples = [tuple(sorted(x)) for x in X]
    C_tuples = [tuple(sorted(c)) for c in C]
    
    # 批量预计算所有解的适应度
    solutions_to_evaluate = set(X_tuples + C_tuples)
    for sol in solutions_to_evaluate:
        if sol not in edv_cache:
            edv_cache[sol] = EDV(G, list(sol), p)
    
    # 比较并选择
    temp_X = []
    for i in range(pop):
        x_fitness = edv_cache[X_tuples[i]]
        c_fitness = edv_cache[C_tuples[i]]
        
        if x_fitness >= c_fitness:
            temp_X.append(X[i])
        else:
            temp_X.append(C[i])
    
    return temp_X

# 优化的局部搜索函数
def optimized_local_search(xi, G, p, k, max_iterations=3):
    """优化版本的局部搜索，使用缓存和高效数据结构"""
    xi_set = set(xi)
    xi_list = list(xi)
    xi_fitness = cached_edv(G, xi_list, p, k)
    
    improved = True
    iteration = 0
    
    # 预计算所有节点的邻居并缓存
    neighbor_cache = defaultdict(list)
    for node in G.nodes():
        neighbor_cache[node] = list(G.neighbors(node))
    
    while improved and iteration < max_iterations:
        improved = False
        iteration += 1
        
        # 创建所有可能的交换并评估
        best_improvement = 0
        best_swap = None
        
        # 分批处理交换评估，降低内存占用
        batch_size = 50
        
        for idx, current_node in enumerate(xi_list):
            # 使用缓存的邻居列表
            neighbors = neighbor_cache[current_node]
            # 高效过滤已在种子集中的邻居
            valid_neighbors = [n for n in neighbors if n not in xi_set]
            
            # 批量评估
            for i in range(0, len(valid_neighbors), batch_size):
                batch = valid_neighbors[i:i+batch_size]
                for neighbor in batch:
                    # 创建新的种子集
                    new_xi = xi_list.copy()
                    new_xi[idx] = neighbor
                    new_fitness = cached_edv(G, new_xi, p, k)
                    improvement = new_fitness - xi_fitness
                    
                    if improvement > best_improvement:
                        best_improvement = improvement
                        best_swap = (current_node, neighbor, idx)
        
        # 如果找到改进，执行交换
        if best_swap and best_improvement > 0:
            current_node, neighbor, idx = best_swap
            xi_set.remove(current_node)
            xi_set.add(neighbor)
            xi_list[idx] = neighbor
            xi_fitness += best_improvement
            improved = True
    
    return xi_list

# 优化的差分进化算法主函数
def de(G, n, k, p, max_iter=100, F=0.6, cr=0.4):
    """
    优化版差分进化算法
    
    参数:
    G - 图
    n - 种群大小
    k - 种子集大小
    p - 传播概率
    max_iter - 最大迭代次数
    F - 变异因子
    cr - 交叉概率
    
    返回:
    best_solution - 最佳解
    fitness_history - 适应度历史
    terrain_counts - 地形统计
    local_search_count - 局部搜索次数
    evolution_data - 进化轨迹数据
    landscape_data - 适应度地形数据
    """
    # 初始化参数
    nodelist = list(G.nodes())
    fitness_history = []
    local_search_count = 0
    
    # 添加进化轨迹数据收集列表
    evolution_data = []
    
    # 地形统计
    terrain_counts = {"peak": 0, "ridge": 0, "plateau": 0}
    peak_to_other_transitions = {"peak_to_ridge": 0, "peak_to_plateau": 0}
    peak_maintained_count = 0  # 新增：记录保持山峰地形的次数
    
    # 添加用于地形图的数据收集
    landscape_data = []
    
    # 初始化种群
    X = degree_initialization(G, n, k)
    
    # 预计算初始种群适应度
    unique_solutions = {tuple(sorted(ind)) for ind in X}
    for sol in unique_solutions:
        edv_cache[sol] = EDV(G, list(sol), p)
    
    # 获取当前最佳解
    population_fitness = [cached_edv(G, ind, p) for ind in X]
    current_best = max(population_fitness)
    current_best_idx = population_fitness.index(current_best)
    
    best_solution = X[current_best_idx].copy()
    best_fitness = current_best
    fitness_history.append(best_fitness)
    
    # 初始化地形
    current_terrain = "ridge"
    terrain_counts[current_terrain] = 1
    
    # 进化控制变量
    stagnation = 0
    last_fitness = best_fitness
    u_history = []
    
    # 主进化循环
    for iter in range(max_iter):
        # 地形判断
        if stagnation >= 3 and current_best == last_fitness:
            previous_terrain = current_terrain
            # 使用随机游走探测
            current_terrain = random_walk_measurement(G, X, nodelist, k, num_walks=100, p=p)
            terrain_counts[current_terrain] += 1
            
            if previous_terrain == "peak" and current_terrain != "peak":
                transition_key = f"peak_to_{current_terrain}"
                peak_to_other_transitions[transition_key] = peak_to_other_transitions.get(transition_key, 0) + 1
            
            stagnation = 0
        
        last_fitness = current_best
        
        # 计算特征值u - 仅在需要时计算
        if iter % 10 == 0 or stagnation >= 3:
            u = calculate_u(X, best_solution, G, p)
            u_history.append(u)
            print(f"第{iter+1}代: u={u:.3f}")
        else:
            u = u_history[-1] if u_history else None
        
        # 山峰地形特殊处理
        if current_terrain == "peak":
            worse = same = improved = 0
            
            # 计算所有个体的适应度
            individual_fitness = [(i, cached_edv(G, ind, p)) for i, ind in enumerate(X)]
            
            # 按适应度降序排序
            individual_fitness.sort(key=lambda x: x[1], reverse=True)
            
            # 只选择适应度最高的个体进行局部搜索
            best_individual_idx = individual_fitness[0][0]
            
            # 对最优个体执行局部搜索
            optimized = optimized_local_search(X[best_individual_idx].copy(), G, p, k)
            original_fitness = cached_edv(G, X[best_individual_idx], p)
            new_fitness = cached_edv(G, optimized, p)
            
            local_search_count += 1
            
            if new_fitness < original_fitness:
                worse += 1
            elif new_fitness == original_fitness:
                same += 1
                X[best_individual_idx] = optimized
            else:
                improved += 1
                X[best_individual_idx] = optimized
                print(f"最优个体 - 适应度提升: {original_fitness:.4f} -> {new_fitness:.4f} (改进: +{new_fitness-original_fitness:.4f})")
            
            # 动态判断地形
            previous_terrain = current_terrain
            
            if improved > 0:
                current_terrain = "ridge"
                print(f"从山峰地形切换到山脊地形")
            elif worse > 0:
                current_terrain = "peak"
                print(f"保持为山峰地形")
                peak_maintained_count += 1  # 新增：统计保持山峰地形的次数
            elif same > 0:
                current_terrain = "plateau"
                print(f"从山峰地形切换到高原地形")
            else:
                current_terrain = "ridge"
            
            terrain_counts[current_terrain] += 1
            
            # 添加这段代码来跟踪从peak到其他地形的转换
            if previous_terrain == "peak" and current_terrain != "peak":
                transition_key = f"peak_to_{current_terrain}"
                peak_to_other_transitions[transition_key] = peak_to_other_transitions.get(transition_key, 0) + 1
        
        # 差分变异
        M = Differential_Mutation(G, X, F, n, k, nodelist, p=p, terrain=current_terrain, u=u, best_solution=best_solution)
        # 交叉
        C = Crossover(X, M, cr, n, k, nodelist)
        # 选择
        X = Selection(G, X, C, n, p)
       
        # 评估当前种群
        current_fitness = [cached_edv(G, ind, p) for ind in X]
        current_best = max(current_fitness)
        best_idx = current_fitness.index(current_best)
        
        # 更新全局最优解
        if current_best > best_fitness:
            best_fitness = current_best
            best_solution = X[best_idx].copy()
            stagnation = 0
            print(f"第{iter+1}代 | 最佳适应度: {current_best:.4f} (改进)")
        else:
            stagnation += 1
            print(f"第{iter+1}代 | 最佳适应度: {current_best:.4f}")
        
        # 更新适应度历史
        fitness_history.append(current_best)
        
        # 收集当前迭代的进化轨迹数据
        distances = [calculate_distance(ind, best_solution) for ind in X]
        evolution_data.append({
            'iteration': iter,
            'distances': distances,
            'fitness': current_fitness,
            'individuals': [ind.copy() for ind in X]  # 添加个体信息
        })
        
        # 收集适应度地形数据
        landscape_data.extend([(ind.copy(), fit) for ind, fit in zip(X, current_fitness)])
    
    # 移除绘图函数调用
    # plot_evolution_trajectory(evolution_data)
    # plot_fitness_landscape(landscape_data)
    # plot_evolution_on_landscape(evolution_data, interval=5)
    
    print(f"\n地形转换统计:")
    print(f"保持山峰地形: {peak_maintained_count}次")
    for transition, count in peak_to_other_transitions.items():
        transition_name = "山峰到山脊" if transition == "peak_to_ridge" else "山峰到高原"
        print(f"{transition_name}({transition}): {count}次")
    
    # 返回附加数据
    return best_solution, fitness_history, terrain_counts, local_search_count, evolution_data, landscape_data

# 主函数
def main():
    # 重置全局缓存
    global edv_cache
    edv_cache = {}
    network_path = "D:\\VS\\code\\networks\\netscience.txt"
    # network_path = "D:\\VS\\code\\networks\\email.txt"
    # network_path = "D:\\VS\\code\\networks\\blog.txt"
    # network_path = "D:\\VS\\code\\networks\\CA-HepTh.txt"
    # network_path = "D:\\VS\\code\\networks\\NetHEHT.txt"

    G = gen_graph(network_path)
    p = 0.05
    start_time = time.time()
    
    # 串行预计算节点评分
    for node in G.nodes():
        cached_edv(G, [node], p)  # 预热缓存
    
    # 设置差分进化算法参数
    F = 0.6  # 变异因子
    cr = 0.4  # 交叉概率
    
    # 运行主算法，将F和cr作为参数传入
    best_seed, fitness_history, terrain_counts, local_search_count, evolution_data, landscape_data = de(G, n=30, k=30, p=p, max_iter=100, F=F, cr=cr)
    
    # 输出结果摘要
    print("\n===== 结果摘要 =====")
    for terrain, count in terrain_counts.items():
        terrain_name = "山峰" if terrain == "peak" else "山脊" if terrain == "ridge" else "高原"
        print(f"{terrain_name}({terrain}): {count}次")
    
    print(f"局部搜索执行次数: {local_search_count}次")
    
    # 评估最终结果
    ic_value = IC(G, best_seed, p, mc=10000)
    print(f"Network: {network_path}")  # 直接输出网络文件路径
    print(f"seed set (size: {len(set(best_seed))}): {best_seed}")
    print(f"IC influence: {ic_value:.4f}")
    print(f"running time: {time.time() - start_time:.2f} seconds")
    
    # 绘制适应度历史曲线
    # plot_fitness_history(fitness_history, 'fitness_history.png')
    
    # 绘制进化轨迹图
    # plot_evolution_trajectory(evolution_data)
    
    # 绘制进化在适应度地形上的轨迹
    plot_evolution_on_landscape(evolution_data, interval=5, show_iteration_colorbar=False)  # 每5代取样一次
    
    # 也可以选择性地绘制适应度地形图
    # plot_fitness_landscape(landscape_data)

if __name__ == "__main__":
    main()
