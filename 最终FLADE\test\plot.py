import matplotlib.pyplot as plt
import matplotlib as mpl
from matplotlib.gridspec import GridSpec

def plot_fitness_and_kurtosis(fitness_history, kurtosis_stats, save_path='fitness_and_kurtosis.png', kurtosis_threshold=3):
    """
    将适应度历史和峰度值变化合并在一个双Y轴图表中展示
    """
    # 防止负号显示异常
    plt.rcParams['axes.unicode_minus'] = False

    # 提取峰度数据
    iterations = [item[0] for item in kurtosis_stats]
    kurtosis_values = [item[1] for item in kurtosis_stats]

    # 创建图表
    plt.figure(figsize=(10, 6), dpi=300)
    gs = GridSpec(1, 1)
    ax1 = plt.subplot(gs[0, 0])

    # 绘制适应度曲线
    color_fitness = '#1f77b4'
    line1 = ax1.plot(range(len(fitness_history)), fitness_history, color=color_fitness, linewidth=2, label='Best Fitness')[0]
    ax1.set_xlabel('Iterations', fontsize=12)
    ax1.set_ylabel('Fitness Value', fontsize=12, color=color_fitness)
    ax1.tick_params(axis='y', labelcolor=color_fitness)
    ax1.grid(linestyle='--', alpha=0.3)

    # 标记最佳适应度点
    best_idx = fitness_history.index(max(fitness_history))
    scatter1 = ax1.scatter(best_idx, fitness_history[best_idx], color='green', s=50, zorder=5, label=f'Best: {fitness_history[best_idx]:.4f}')

    # 绘制峰度曲线（第二Y轴）
    ax2 = ax1.twinx()
    color_kurtosis = '#ff7f0e'
    line2 = ax2.plot(iterations, kurtosis_values, color=color_kurtosis, linewidth=2, linestyle='-', label='Kurtosis')[0]
    ax2.set_ylabel('Kurtosis Value', fontsize=12, color=color_kurtosis)
    ax2.tick_params(axis='y', labelcolor=color_kurtosis)

    # 添加峰度阈值线
    line3 = ax2.axhline(y=kurtosis_threshold, color='red', linestyle='--', linewidth=1, label=f'Threshold ({kurtosis_threshold:.2f})')

    # 标记超过阈值的点
    above_x = [iterations[i] for i, k in enumerate(kurtosis_values) if k > kurtosis_threshold]
    above_y = [k for k in kurtosis_values if k > kurtosis_threshold]
    scatter2 = ax2.scatter(above_x, above_y, color='red', s=30, zorder=5, label='Above Threshold')

    # 合并图例 - 将图例放在图表下方，避免遮挡曲线
    handles = [line1, scatter1, line2, line3, scatter2]
    labels = [h.get_label() for h in handles]
    ax1.legend(handles, labels, loc='upper center', fontsize=10, ncol=3, 
               bbox_to_anchor=(0.5, -0.15), frameon=True)

    # 设置标题
    plt.title('Fitness and Kurtosis Trends', fontsize=14)

    # 美化边框
    for spine in ax1.spines.values():
        spine.set_linewidth(1)

    # 调整布局，保存图片 - 增加bottom参数为图例留出空间
    plt.tight_layout(rect=[0, 0.1, 1, 1])
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()
