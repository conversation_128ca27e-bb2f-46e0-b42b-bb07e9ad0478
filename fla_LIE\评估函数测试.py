
import networkx as nx
import numpy as np
import time

def gen_graph(filename: str) -> nx.Graph:
    """
    从文件加载图形数据并生成无向图
    
    Args:
        filename: 包含边列表的文件路径
        
    Returns:
        nx.Graph: 生成的无向图对象
        
    Raises:
        RuntimeError: 当图加载失败时
    """
    try:
        G = nx.Graph()  # 创建一个空的无向图
        edges_data = np.loadtxt(filename, skiprows=1, usecols=[0, 1])  # 读取边数据
        edges = [(int(u), int(v)) for u, v in edges_data]
        G.add_edges_from(edges)  # 将边添加到图中
        return G  # 返回生成的图
    except Exception as e:
        raise RuntimeError(f"加载图形错误: {e}")

def EDV(S, G,p):
    S = set(S)  # 转换S为集合以去除重复项
    total = len(S)
    NS_1_S = set()  # 存储S的所有邻居，但不包括S中的节点

    # 收集S的所有邻居节点
    for s in S:
        NS_1_S.update(G[s])

    # 从邻居集中去除S中已存在的节点
    NS_1_S.difference_update(S)

    # 计算每个邻居节点对总值的贡献
    for u in NS_1_S:
        r = sum(1 for s in S if u in G[s])  # 检查是否存在边s-u
        total += (1 - (1 - p) ** r)

    return total

def optimized_EDV(graph, S, p):
    """
    优化后的影响力评估 (Expected Diffusion Value)
    
    Args:
        graph: NetworkX图对象
        S: 种子节点集合
        p: 传播概率
        
    Returns:
        float: 估计的影响力值
    """
    # 预生成邻接字典（将邻居存储为集合）
    adj_dict = {node: set(graph.neighbors(node)) for node in graph.nodes()}
    S = set(S)
    
    # 计算一阶邻居 (NS_1)，直接使用集合操作
    NS_1 = {neighbor for s in S for neighbor in adj_dict.get(s, set())} - S
    
    # 快速计算每个邻居的连接数
    influence_sum = 0
    for node in NS_1:
        num_connections = len(adj_dict.get(node, set()) & S)
        influence_sum += 1 - (1 - p) ** num_connections
    
    return len(S) + influence_sum

def LIE_two_hop(s_set, G, p):
    s_set = list(s_set)
    LIE_num = len(s_set)
    temp_sum = 0
    temp_sum_2 = 0
    neighbor_s=set()
    neighbor_ss=set()
    for i in s_set:
        neighbors = set(G.neighbors(i)) - set(s_set) #一阶邻居
        neighbors_un = set()  # 用来存储所有不在x中的二级邻居
        for neighbor in neighbors:
            neighbors_of_neighbor = set(G.neighbors(neighbor)) - set(s_set) - set(neighbors)
            neighbors_un.update(neighbors_of_neighbor)

        neighbor_ss.update(neighbors_un)
        neighbor_s.update(neighbors)
    for i in neighbor_s:
        num = sum(1 for j in s_set if i in G[j])
        temp_sum += (1 - (1 - p) ** num)
    num2 = 0
    for i in neighbor_ss:
        for j in neighbor_s:
            if i in G[j]:
                num2 += 1

    if neighbor_s:
        temp_sum_2 = (temp_sum * num2 * p) / len(neighbor_s)

    LIE = LIE_num + temp_sum + temp_sum_2
    return LIE

# 在向量化计算部分使用更高精度的数据类型
def vectorized_newLIE_two_hop(s_set, G, p):
    """向量化优化的二跳影响力估计函数
    
    Args:
        s_set (set): 种子节点集合
        G (nx.Graph): 网络图对象
        p (float): 传播概率
        
    Returns:
        float: 基于向量化计算的影响力估计值
    """
    s_set = set(s_set)  # 确保输入为集合类型
    # 预生成邻接字典加速查询（节点到邻居集合的映射）
    adj_dict = {node: set(G.neighbors(node)) for node in G.nodes()}
    
    # 初始化邻居集合
    neighbor_s = set()  # 存储一阶邻居
    neighbor_ss = set()  # 存储二阶邻居
    
    # 分种子节点计算邻居
    for node in s_set:
        # 当前种子节点的一阶邻居（排除种子节点自身）
        current_neighbors = adj_dict[node] - s_set
        neighbor_s.update(current_neighbors)  # 合并到全局一阶集合
        
        # 计算当前种子节点的二阶邻居
        for neighbor in current_neighbors:
            # 二阶邻居定义：邻居的邻居，排除种子节点和当前种子的一阶邻居
            second_neighbors = adj_dict[neighbor] - s_set - current_neighbors
            neighbor_ss.update(second_neighbors)
    
    # 向量化计算一阶影响力部分
    # 生成每个一阶邻居与种子节点的连接数数组
    # 修改数据类型为兼容的float64
    connection_counts = np.array([len(adj_dict[n] & s_set) for n in neighbor_s], dtype=np.float64)
    temp_sum = np.sum(1 - (1 - np.float64(p)) ** connection_counts)
    
    # 计算二阶影响力部分
    num2 = sum(len(adj_dict[ss] & neighbor_s) for ss in neighbor_ss)  # 二阶到一阶的连接总数
    # 计算二阶影响力修正项（防止空集合导致的除零错误）
    temp_sum_2 = (temp_sum * num2 * p) / len(neighbor_s) if neighbor_s else 0
    
    # 总影响力 = 种子数 + 一阶影响力 + 二阶影响力
    return len(s_set) + temp_sum + temp_sum_2


def main():
    network_path = "D:\\VS\\code\\networks\\blog.txt"
    G = gen_graph(network_path)
    p = 0.1
    k = 100  # 设置要选择的顶点数量
    
    # 直接在main函数中计算度中心性并选择前k个节点
    degree_centrality = nx.degree_centrality(G)
    sorted_nodes = sorted(degree_centrality.items(), 
                         key=lambda x: x[1], 
                         reverse=True)
    top_k_nodes = [node for node, _ in sorted_nodes[:k]]
    
    # 计算EDV并统计时间
    start_time = time.time()
    edv = EDV(top_k_nodes, G, p)
    edv_time = time.time() - start_time
    
    start_time = time.time()
    edv_optimized = optimized_EDV(G, top_k_nodes, p)
    edv_opt_time = time.time() - start_time
    
    # 计算LIE并统计时间
    
    start_time = time.time()
    LIE_two_hop_ = LIE_two_hop(top_k_nodes, G, p)
    lie_time = time.time() - start_time
    
    start_time = time.time()
    vectorized_newLIE_two_hop_result = vectorized_newLIE_two_hop(top_k_nodes, G, p)
    vec_lie_time = time.time() - start_time

    # 打印计算结果和时间统计
    print("计算结果和时间统计:")
    print(f"EDV: {edv} (耗时: {edv_time:.10f}秒)")
    print(f"优化后的EDV: {edv_optimized} (耗时: {edv_opt_time:.10f}秒)")
    print(f"LIE_two_hop_: {LIE_two_hop_} (耗时: {lie_time:.10f}秒)")
    print(f"vectorized_newLIE_two_hop_: {vectorized_newLIE_two_hop_result} (耗时: {vec_lie_time:.10f}秒)")
    
    # 计算并打印时间提升比例
    print("\n时间性能提升比例:")
    if edv_opt_time > 0:
        print(f"优化后EDV相比原始EDV提升: {(edv_time/edv_opt_time - 1)*100:.2f}%")
    else:
        print("优化后EDV执行时间过短无法计算提升比例")
    
    if vec_lie_time > 0:
        print(f"向量化LIE相比原始LIE提升: {(lie_time/vec_lie_time - 1)*100:.2f}%")
    else:
        print("向量化LIE执行时间过短无法计算提升比例")

if __name__ == "__main__":
    main()