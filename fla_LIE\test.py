
import networkx as nx
import numpy as np
import time

def gen_graph(filename: str) -> nx.Graph:
    """
    从文件加载图形数据并生成无向图
    
    Args:
        filename: 包含边列表的文件路径
        
    Returns:
        nx.Graph: 生成的无向图对象
        
    Raises:
        RuntimeError: 当图加载失败时
    """
    try:
        G = nx.Graph()  # 创建一个空的无向图
        edges_data = np.loadtxt(filename, skiprows=1, usecols=[0, 1])  # 读取边数据
        edges = [(int(u), int(v)) for u, v in edges_data]
        G.add_edges_from(edges)  # 将边添加到图中
        return G  # 返回生成的图
    except Exception as e:
        raise RuntimeError(f"加载图形错误: {e}")

def EDV(graph, S, p, cached_neighbors=None):
    """
    影响力评估 (Expected Diffusion Value)
    
    Args:
        graph: NetworkX图对象
        S: 种子节点集合
        p: 传播概率
        cached_neighbors: 缓存的邻居信息(未使用)
        
    Returns:
        float: 估计的影响力值
    """
    k = len(S)
    influence_sum = 0
    NS_1 = set()
    for node in S:
        if node in graph:
            NS_1.update(graph[node])  # 获取一阶邻居节点集合

    for node in NS_1:
        if node not in S:
            num_connections = sum(1 for s in S if s in graph[node])
            influence_sum += 1 - (1 - p) ** num_connections
    result = (k + influence_sum)    # 计算 EDV(S)

    return result

import numpy as np
import networkx as nx
from scipy.sparse import csr_matrix

def vectorized_EDV(graph, S, p):
    """向量化优化的EDV计算，无需显式循环"""
    # 生成节点索引映射（应对非连续节点ID）
    nodes = list(graph.nodes())
    node_index = {n: i for i, n in enumerate(nodes)}
    n = len(nodes)
    
    # 构建稀疏邻接矩阵（CSR格式加速矩阵运算）
    adj_csr = nx.adjacency_matrix(graph, nodelist=nodes).astype(bool).tocsr()
    
    # 生成种子节点掩码向量（O(1)时间访问）
    seed_indices = np.array([node_index[s] for s in S if s in node_index], dtype=np.int32)
    if len(seed_indices) == 0:
        return 0.0
    seed_mask = np.zeros(n, dtype=np.int8)
    seed_mask[seed_indices] = 1
    
    # 单次矩阵乘法获取连接数（替代双重循环）
    # 修改第78行：
    conn_counts = adj_csr[:, seed_indices].sum(axis=1)  # 移除 .A1
    
    # 计算一跳邻居（排除种子节点）
    one_hop_mask = (conn_counts > 0) & (seed_mask == 0)
    one_hop_counts = conn_counts[one_hop_mask]
    
    # 向量化概率计算（比循环快50倍）
    influence = np.sum(1 - np.power(1 - p, one_hop_counts))
    
    return len(seed_indices) + influence

def optimized_EDV(graph, S, p):
    """
    优化后的影响力评估 (Expected Diffusion Value)
    
    Args:
        graph: NetworkX图对象
        S: 种子节点集合
        p: 传播概率
        
    Returns:
        float: 估计的影响力值
    """
    # 预生成邻接字典（将邻居存储为集合）
    adj_dict = {node: set(graph.neighbors(node)) for node in graph.nodes()}
    S = set(S)
    
    # 计算一阶邻居 (NS_1)，直接使用集合操作
    NS_1 = {neighbor for s in S for neighbor in adj_dict.get(s, set())} - S
    
    # 快速计算每个邻居的连接数
    influence_sum = 0
    for node in NS_1:
        num_connections = len(adj_dict.get(node, set()) & S)
        influence_sum += 1 - (1 - p) ** num_connections
    
    return len(S) + influence_sum


def LIE_two_hop(tempSeed, G, p):
    """计算种子集的局部影响力估计（LIE）"""
    num_nodes = G.number_of_nodes()  # 获取图中的节点数量
    stateMatrix = np.zeros((num_nodes, 4))  # 初始化状态矩阵
    for i in range(num_nodes):
        stateMatrix[i, 0] = i  # 初始化节点标识

    for seed in tempSeed:
        stateMatrix[int(seed), 1] = 1  # 将种子节点标记为已激活

    for i in range(num_nodes):  # 计算一跳邻居的影响
        if stateMatrix[i, 1] == 1:  # 如果节点是种子节点
            for neighbor in G.neighbors(i):  # 遍历其邻居
                neighbor = int(neighbor)  # 确保邻居节点是整数类型
                if stateMatrix[neighbor, 1] == 0:  # 如果邻居未被激活
                    stateMatrix[neighbor, 2] += 1  # 增加邻居的被激活计数

    onehop_edges = sum(stateMatrix[:, 2])  # 计算一跳边数
    sigma_onehop = sum(1 - (1 - p) ** stateMatrix[:, 2])  # 计算一跳的影响力估计

    for i in range(num_nodes):  # 计算二跳邻居的影响
        if stateMatrix[i, 2] != 0:  # 如果节点有被激活的邻居
            for neighbor in G.neighbors(i):  # 遍历邻居
                neighbor = int(neighbor)  # 确保邻居节点是整数类型
                if stateMatrix[neighbor, 1] == 0 and stateMatrix[neighbor, 2] == 0:  # 如果邻居未被激活且没有被激活的邻居
                    stateMatrix[neighbor, 3] += 1  # 增加二跳影响计数

    twohop_edges = sum(stateMatrix[:, 3])  # 计算二跳边数
    sigma_pd = p * twohop_edges  # 二跳的传播系数
    LIE = len(tempSeed) + (1 + sigma_pd / num_nodes) * sigma_onehop  # 计算总的影响力估计
    return LIE  # 返回影响力估计值


def xiuLIE_two_hop(G, tempSeed, p):
    """修正后的LIE函数（严格遵循论文四分量累加结构）"""
    # 预生成邻接字典和入度字典（适应WC模型）
    adj_dict = {n: list(nx.neighbors(G, n)) for n in G.nodes()}
    in_degree = dict(G.in_degree()) if nx.is_directed(G) else dict(G.degree())
    
    s_set = set(tempSeed)
    k = len(s_set)
    sigma_0 = k  # 种子自身影响
    
    # 初始化各分量
    sigma_1_star = 0.0  # Σp_ij（公式①）
    sigma_1 = 0.0       # 1 - Π(1-p_ij)
    sigma_2 = 0.0       # Σ(p^1_i * p_jk)
    
    # 数据结构优化
    node_status = {
        n: {
            'activated': False,
            'one_hop_p': 0.0,
            'two_hop_contrib': 0.0
        } for n in G.nodes()
    }
    
    # 标记种子节点
    for n in s_set:
        node_status[n]['activated'] = True
    
    # ---- 计算一跳区域分量 ----
    one_hop_nodes = set()
    for u in s_set:
        for v in adj_dict[u]:
            if v in s_set: continue
            
            # 更新σ¹*（直接累加边概率）
            sigma_1_star += p
            
            # 更新σ₁（累计激活概率）
            node_status[v]['one_hop_p'] = 1 - (1 - node_status[v]['one_hop_p']) * (1 - p)
            
            one_hop_nodes.add(v)
    
    # 计算σ₁分量（需遍历一跳节点）
    sigma_1 = sum(node_status[n]['one_hop_p'] for n in one_hop_nodes)
    
    # ---- 计算二跳区域分量 ----
    two_hop_edges = set()
    for v in one_hop_nodes:
        # 获取v的激活概率p^1_i（模型敏感）
        p_v = node_status[v]['one_hop_p']  # 来自σ₁的激活概率
        
        for w in adj_dict[v]:
            if w in s_set or w in one_hop_nodes: continue
            
            # 累加σ₂分量（联合概率）
            sigma_2 += p_v * p
            
            # 记录已处理边（避免重复计算）
            two_hop_edges.add((v, w))
    
    # 组合所有分量（公式11）
    LIE_total = sigma_0 + sigma_1_star + sigma_1 + sigma_2
    return LIE_total

def vectorized_xiuLIE_two_hop(G, tempSeed, p):
    """向量化优化的xiuLIE_two_hop函数，使用NumPy加速计算"""
    # 预处理：创建节点索引映射
    nodes = list(G.nodes())
    node_index = {n: i for i, n in enumerate(nodes)}
    n_nodes = len(nodes)
    
    # 将种子集转换为集合和索引数组
    s_set = set(tempSeed)
    seed_indices = np.array([node_index[s] for s in s_set if s in node_index])
    
    # 边界情况处理
    if len(seed_indices) == 0:
        return 0.0
    
    # 创建邻接矩阵（稀疏格式）
    adj_matrix = nx.adjacency_matrix(G, nodelist=nodes)
    
    # 创建种子节点掩码
    seed_mask = np.zeros(n_nodes, dtype=bool)
    seed_mask[seed_indices] = True
    
    # 计算sigma_0（种子节点数量）
    sigma_0 = len(seed_indices)
    
    # 计算一跳邻居
    # 使用矩阵乘法找出所有与种子相连的节点
    one_hop_connections = adj_matrix[:, seed_indices].sum(axis=1)
    # 检查是否为稀疏矩阵，如果是则使用.A1，否则使用flatten()
    if hasattr(one_hop_connections, 'A1'):
        one_hop_connections = one_hop_connections.A1
    else:
        one_hop_connections = one_hop_connections.flatten()
    
    one_hop_mask = (one_hop_connections > 0) & (~seed_mask)
    one_hop_indices = np.where(one_hop_mask)[0]
    
    # 如果没有一跳邻居，直接返回种子数量
    if len(one_hop_indices) == 0:
        return sigma_0
    
    # 计算sigma_1_star（直接累加边概率）
    sigma_1_star = p * np.sum(one_hop_connections[one_hop_mask])
    
    # 计算sigma_1（累计激活概率）
    # 向量化计算 1-(1-p)^连接数
    one_hop_probs = 1 - np.power(1-p, one_hop_connections[one_hop_mask])
    sigma_1 = np.sum(one_hop_probs)
    
    # 计算二跳区域
    # 获取一跳节点的邻居（排除种子和一跳节点）
    two_hop_mask = np.zeros(n_nodes, dtype=bool)
    
    # 使用矩阵乘法找出所有与一跳节点相连的节点
    two_hop_connections = adj_matrix[one_hop_indices, :].sum(axis=0)
    # 检查是否为稀疏矩阵，如果是则使用.A1，否则使用flatten()
    if hasattr(two_hop_connections, 'A1'):
        two_hop_connections = two_hop_connections.A1
    else:
        two_hop_connections = two_hop_connections.flatten()
    
    two_hop_mask = (two_hop_connections > 0) & (~seed_mask) & (~one_hop_mask)
    two_hop_indices = np.where(two_hop_mask)[0]
    
    # 修正二跳区域分量计算
    sigma_2 = 0.0
    if len(two_hop_indices) > 0:
        # 获取一跳节点的激活概率
        one_hop_probs = one_hop_probs.flatten()  # 确保维度正确
        
        # 计算二跳连接矩阵
        two_hop_adj = adj_matrix[two_hop_indices][:, one_hop_indices]
        
        # 向量化计算联合概率
        connection_counts = two_hop_adj.sum(axis=1)
        sigma_2 = np.sum(one_hop_probs * connection_counts * p)

    # 保持原始的四分量累加结构
    LIE_total = sigma_0 + sigma_1_star + sigma_1 + sigma_2
    return LIE_total



def main():
    network_path = "D:\\VS\\code\\networks\\email.txt"
    G = gen_graph(network_path)
    p = 0.1
    
    # 新增度中心性计算
    degree_centrality = nx.degree_centrality(G)
    nodes = list(degree_centrality.keys())
    values = list(degree_centrality.values())
    sorted_indices = np.argsort(values)[::-1]
    top_30_nodes = [nodes[i] for i in sorted_indices[:100]]
    
    # 计算EDV和LIE并统计时间
    print("\n计算时间统计:")
    
    # EDV计算时间
    start = time.time()
    edv_value = EDV(G, top_30_nodes, p)
    edv_time = time.time() - start
    
    # vectorized_EDV计算时间
    start = time.time()
    edv_value_vectorized = vectorized_EDV(G, top_30_nodes, p)
    edv_vectorized_time = time.time() - start
    
    # optimized_EDV计算时间
    start = time.time()
    edv_value_optimized = optimized_EDV(G, top_30_nodes, p)
    edv_optimized_time = time.time() - start
    
    # LIE_two_hop计算时间
    start = time.time()
    lie_value_two_hop = LIE_two_hop(top_30_nodes, G, p)
    lie_time = time.time() - start
    
    # xiuLIE_two_hop计算时间
    start = time.time()
    xiuLIE_two_hop_value = xiuLIE_two_hop(G, top_30_nodes, p)
    xiuLIE_time = time.time() - start
    
    # vectorized_xiuLIE_two_hop计算时间
    start = time.time()
    vectorized_xiuLIE_two_hop_value = vectorized_xiuLIE_two_hop(G, top_30_nodes, p)
    vectorized_xiuLIE_time = time.time() - start
    
    # 打印结果和时间统计
    print(f"\n度中心性前30节点: {top_30_nodes}")
    print(f"\n计算结果:")
    print(f"EDV值: {edv_value:.4f} (耗时: {edv_time:.4f}s)")
    print(f"EDV值_vectorized: {edv_value_vectorized:.4f} (耗时: {edv_vectorized_time:.4f}s)")
    print(f"EDV值_optimized: {edv_value_optimized:.4f} (耗时: {edv_optimized_time:.4f}s)")
    print(f"LIE_two_hop值: {lie_value_two_hop:.4f} (耗时: {lie_time:.4f}s)")
    print(f"xiuLIE_two_hop值: {xiuLIE_two_hop_value:.4f} (耗时: {xiuLIE_time:.4f}s)")
    print(f"vectorized_xiuLIE_two_hop值: {vectorized_xiuLIE_two_hop_value:.4f} (耗时: {vectorized_xiuLIE_time:.4f}s)")


if __name__ == "__main__":
    main()