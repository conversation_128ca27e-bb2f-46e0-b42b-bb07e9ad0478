import numpy as np
import networkx as nx
import random
import time
from collections import defaultdict
from base_fun import IC, gen_graph, EDV, degree_initialization

# 全局缓存，避免重复计算EDV
edv_cache = {}

# 包装EDV函数，使用全局缓存
def cached_edv(G, seed_set, p):
    """缓存版本的EDV，避免重复计算"""
    key = (tuple(sorted(seed_set)), p)
    if key not in edv_cache:
        edv_cache[key] = EDV(G, seed_set, p)
    return edv_cache[key]

# 标准DE/rand/1/bin变异算子
def differential_mutation(X, F, pop, k, nodelist):
    """
    标准DE/rand/1/bin变异操作
    X: 当前种群
    F: 缩放因子
    pop: 种群大小
    k: 种子集大小
    nodelist: 节点列表
    """
    M = []  # 存储变异后的种群
    
    for i in range(pop):
        # 随机选择三个不同的个体（不包括当前个体i）
        available_indices = [j for j in range(pop) if j != i]
        if len(available_indices) < 3:  # 确保有足够的个体可选
            available_indices = list(range(pop))
        
        r1, r2, r3 = random.sample(available_indices, 3)
        
        # 创建变异个体，基于 DE/rand/1/bin 策略
        # M_i = X_r1 + F * (X_r2 - X_r3)
        
        # 创建变异个体的基础（复制r1）
        v_i = X[r1].copy()
        
        # 计算差向量 (X_r2 - X_r3)
        diff_set = set(X[r2]) - set(X[r3])
        # 如果差异太少，随机增加一些差异
        if len(diff_set) < 2:
            diff_set.update(random.sample(list(set(X[r2])), min(2, len(X[r2]))))
        
        # 根据F的比例应用差向量
        change_count = int(F * len(diff_set))
        change_count = max(1, min(change_count, k//2))  # 确保至少改变1个，最多改变k/2个
        
        # 执行差分变异
        if change_count > 0 and diff_set:
            # 选择要替换的位置
            positions_to_change = random.sample(range(len(v_i)), change_count)
            
            # 选择要使用的节点（从差向量中或所有节点中）
            nodes_to_use = list(diff_set)
            if len(nodes_to_use) < change_count:
                additional_nodes = random.sample(nodelist, change_count - len(nodes_to_use))
                nodes_to_use.extend(additional_nodes)
            
            # 执行替换，确保没有重复
            used_nodes = set(v_i)
            for pos, node_idx in zip(positions_to_change, range(change_count)):
                # 移除当前节点
                used_nodes.remove(v_i[pos])
                
                # 找到一个未使用的节点
                new_node = None
                for potential_node in nodes_to_use:
                    if potential_node not in used_nodes:
                        new_node = potential_node
                        break
                
                # 如果没有找到合适的节点，从所有未使用的节点中选择
                if new_node is None:
                    available_nodes = [n for n in nodelist if n not in used_nodes]
                    if available_nodes:
                        new_node = random.choice(available_nodes)
                    else:
                        # 如果没有可用节点，保留原节点
                        new_node = v_i[pos]
                        used_nodes.add(new_node)
                        continue
                
                # 更新节点
                v_i[pos] = new_node
                used_nodes.add(new_node)
        
        M.append(v_i)
    
    return M

# 交叉操作
def crossover(X, M, CR, pop, k, nodelist):
    """
    二项式交叉操作
    X: 原始种群
    M: 变异后的种群
    CR: 交叉概率
    pop: 种群大小
    k: 种子集大小
    nodelist: 节点列表
    """
    C = []
    
    for i in range(pop):
        # 创建新个体
        c_i = []
        used_nodes = set()
        
        # 至少要有一个变异基因被选中，随机选择一个位置
        j_rand = random.randint(0, k-1)
        
        for j in range(k):
            # 如果是强制变异位置或随机数小于CR，使用变异基因
            if j == j_rand or random.random() < CR:
                if M[i][j] not in used_nodes:
                    c_i.append(M[i][j])
                    used_nodes.add(M[i][j])
                else:
                    # 如果节点已被使用，使用原始个体的基因
                    if X[i][j] not in used_nodes:
                        c_i.append(X[i][j])
                        used_nodes.add(X[i][j])
                    else:
                        # 两者都已使用，找一个新节点
                        available_nodes = [n for n in nodelist if n not in used_nodes]
                        if available_nodes:
                            new_node = random.choice(available_nodes)
                            c_i.append(new_node)
                            used_nodes.add(new_node)
            else:
                # 使用原始个体的基因
                if X[i][j] not in used_nodes:
                    c_i.append(X[i][j])
                    used_nodes.add(X[i][j])
                else:
                    # 原始基因已被使用，使用变异基因
                    if M[i][j] not in used_nodes:
                        c_i.append(M[i][j])
                        used_nodes.add(M[i][j])
                    else:
                        # 两者都已使用，找一个新节点
                        available_nodes = [n for n in nodelist if n not in used_nodes]
                        if available_nodes:
                            new_node = random.choice(available_nodes)
                            c_i.append(new_node)
                            used_nodes.add(new_node)
        
        # 确保我们有k个唯一节点
        if len(c_i) < k:
            available_nodes = [n for n in nodelist if n not in used_nodes]
            needed = k - len(c_i)
            if len(available_nodes) >= needed:
                c_i.extend(random.sample(available_nodes, needed))
            else:
                c_i.extend(available_nodes)
                # 如果仍然不足，添加随机节点（可能会有重复）
                while len(c_i) < k:
                    c_i.append(random.choice(nodelist))
        
        C.append(c_i)
    
    return C

# 选择操作
def selection(G, X, C, pop, p):
    """
    贪婪选择操作
    G: 图
    X: 原始种群
    C: 交叉后的种群
    pop: 种群大小
    p: 传播概率
    """
    new_population = []
    
    for i in range(pop):
        # 计算适应度
        x_fitness = cached_edv(G, X[i], p)
        c_fitness = cached_edv(G, C[i], p)
        
        # 贪婪选择 - 保留更好的个体
        if c_fitness > x_fitness:
            new_population.append(C[i])
        else:
            new_population.append(X[i])
    
    return new_population

# 添加计算偏度和峰度的函数
def calculate_skewness(fitness_values, verbose=False):
    """
    计算适应度序列的偏度并进行离散化
    
    参数:
    fitness_values: 一个列表，包含适应度值
    verbose: 是否返回详细计算过程的信息
    
    返回:
    如果verbose=False:
        sp_sum: 离散化后的序列和
        N: 序列长度
    如果verbose=True:
        一个字典，包含计算过程中的所有中间值
    """
    N = len(fitness_values)
    if N <= 1:
        return (0, N) if not verbose else {"sp_sum": 0, "N": N, "details": "序列太短"}
    
    # 计算差值序列
    differences = [fitness_values[i] - fitness_values[i-1] for i in range(1, N)]
    
    # 计算标准差 σ
    mean_diff_squared = (1/N) * sum(d**2 for d in differences)
    sigma = np.sqrt(mean_diff_squared)
    
    if sigma == 0:  # 避免除以零
        return (0, N) if not verbose else {"sp_sum": 0, "N": N, "details": "标准差为零"}
    
    # 计算偏度 s(i) 序列
    s_values = [(1/N) * (differences[i])**3 / sigma**3 for i in range(len(differences))]
    
    # 离散化: 如果s(i)>0，则为1，否则为0
    sp_sequence = [1 if s > 0 else 0 for s in s_values]
    
    # 计算序列和
    sp_sum = sum(sp_sequence)
    
    if not verbose:
        return sp_sum, N
    else:
        return {
            "sp_sum": sp_sum,
            "N": N,
            "threshold": N/2,
            "differences": differences,
            "sigma": sigma,
            "mean_diff_squared": mean_diff_squared,
            "s_values": s_values,
            "sp_sequence": sp_sequence,
            "fitness_values": fitness_values
        }

# 添加计算峰度的函数
def calculate_kurtosis(fitness_values, verbose=False):
    """
    计算适应度序列的峰度
    
    参数:
    fitness_values: 一个列表，包含适应度值
    verbose: 是否返回详细计算过程的信息
    
    返回:
    如果verbose=False:
        kurtosis: 峰度值
    如果verbose=True:
        一个字典，包含计算过程中的所有中间值
    """
    N = len(fitness_values)
    if N <= 1:
        return 0 if not verbose else {"kurtosis": 0, "N": N, "details": "序列太短"}
    
    # 计算均值
    mean = sum(fitness_values) / N
    
    # 计算标准差
    variance = sum((x - mean) ** 2 for x in fitness_values) / N
    if variance == 0:  # 避免除以零
        return 0 if not verbose else {"kurtosis": 0, "N": N, "details": "方差为零"}
    
    std_dev = np.sqrt(variance)
    
    # 计算四阶中心矩
    fourth_moment = sum((x - mean) ** 4 for x in fitness_values) / N
    
    # 计算峰度
    kurtosis = fourth_moment / (std_dev ** 4)
    
    if not verbose:
        return kurtosis
    else:
        return {
            "kurtosis": kurtosis,
            "N": N,
            "mean": mean,
            "variance": variance,
            "std_dev": std_dev,
            "fourth_moment": fourth_moment,
            "fitness_values": fitness_values
        }

# 添加绘制峰度图的函数
def plot_kurtosis(kurtosis_stats, title="峰度值变化趋势"):
    """
    绘制峰度值随迭代次数变化的折线图
    
    参数:
    kurtosis_stats: 一个列表，包含(迭代次数, 峰度值)的元组
    title: 图表标题
    """
    try:
        import matplotlib.pyplot as plt
        import matplotlib as mpl
        
        # 设置中文字体为微软雅黑
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']  # 指定默认字体为微软雅黑
        plt.rcParams['axes.unicode_minus'] = False  # 解决保存图像时负号'-'显示为方块的问题
        
        # 提取迭代次数和峰度值
        iterations = [item[0] for item in kurtosis_stats]
        kurtosis_values = [item[1] for item in kurtosis_stats]
        
        # 创建图表
        plt.figure(figsize=(10, 6))
        plt.plot(iterations, kurtosis_values, 'b-', linewidth=2, marker='o')
        
        # 添加基准线（峰度=3，表示正态分布）
        plt.axhline(y=3, color='r', linestyle='--', alpha=0.7, label='标准正态分布 (K=3)')
        
        # 添加标题和标签
        plt.title(title, fontsize=15)
        plt.xlabel('迭代次数', fontsize=12)
        plt.ylabel('峰度值', fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.legend()
        
        # 标记峰度>3的点
        for i, k in enumerate(kurtosis_values):
            if k > 3:
                plt.plot(iterations[i], k, 'ro', markersize=8)
        
        # 保存图表
        plt.savefig('kurtosis_plot.png', dpi=300, bbox_inches='tight')
        print(f"峰度变化图已保存至 kurtosis_plot.png")
        
        # 显示图表
        plt.show()
        
    except ImportError:
        print("无法绘制峰度图: 缺少matplotlib库")
        print("请安装matplotlib: pip install matplotlib")

# 修改标准差分进化算法
def de(G, n, k, p, max_iter=100, F=0.6, CR=0.4, verbose_skewness=False, verbose_kurtosis=False):
    """
    标准差分进化算法
    G: 图
    n: 种群大小
    k: 种子集大小
    p: 传播概率
    max_iter: 最大迭代次数
    F: 缩放因子
    CR: 交叉概率
    verbose_skewness: 是否输出详细的偏度计算信息
    verbose_kurtosis: 是否输出详细的峰度计算信息
    """
    # 初始化参数
    nodelist = list(G.nodes())
    fitness_history = []
    skewness_stats = []  # 用于存储每次迭代的偏度统计
    skewness_details = []  # 存储详细计算信息
    kurtosis_stats = []  # 用于存储每次迭代的峰度统计
    kurtosis_details = []  # 存储详细峰度计算信息
    
    # 初始化种群
    X = degree_initialization(G, n, k)
    
    # 评估初始种群
    population_fitness = [cached_edv(G, ind, p) for ind in X]
    best_idx = population_fitness.index(max(population_fitness))
    best_solution = X[best_idx].copy()
    best_fitness = population_fitness[best_idx]
    fitness_history.append(best_fitness)
    
    print(f"初始最佳适应度: {best_fitness:.4f}")
    
    # 主循环
    for iter in range(max_iter):
        # 差分变异
        M = differential_mutation(X, F, n, k, nodelist)
        
        # 交叉
        C = crossover(X, M, CR, n, k, nodelist)
        
        # 选择
        X = selection(G, X, C, n, p)
        
        # 评估当前种群
        current_fitness = [cached_edv(G, ind, p) for ind in X]
        
        # 计算偏度统计并记录
        if verbose_skewness:
            skewness_info = calculate_skewness(current_fitness, verbose=True)
            sp_sum = skewness_info["sp_sum"]
            pop_size = skewness_info["N"]
            skewness_details.append(skewness_info)
        else:
            sp_sum, pop_size = calculate_skewness(current_fitness)
        
        skewness_stats.append((iter+1, sp_sum, pop_size))
        
        # 计算峰度统计并记录
        if verbose_kurtosis:
            kurtosis_info = calculate_kurtosis(current_fitness, verbose=True)
            kurtosis_value = kurtosis_info["kurtosis"]
            kurtosis_details.append(kurtosis_info)
        else:
            kurtosis_value = calculate_kurtosis(current_fitness)
        
        kurtosis_stats.append((iter+1, kurtosis_value))
        
        # 打印每代的详细偏度计算信息
        # print(f"\n迭代 {iter+1} | 偏度计算:")
        # print(f"  - 适应度值: {current_fitness}")
        # if verbose_skewness:
        #     print(f"  - 差值序列: {[f'{d:.4f}' for d in skewness_info['differences']]}")
        #     print(f"  - 标准差(σ): {skewness_info['sigma']:.4f}")
        #     print(f"  - 偏度序列: {[f'{s:.4f}' for s in skewness_info['s_values']]}")
        #     print(f"  - 离散化后: {skewness_info['sp_sequence']}")
        # print(f"  - 偏度值(sp_sum): {sp_sum}, 阈值: {pop_size/2}")
        # print(f"  - 偏度状态: {'超过阈值' if sp_sum > pop_size/2 else '正常'}")
        
        # # 检查偏度
        # if sp_sum > pop_size / 2:
        #     print(f"迭代 {iter+1} | 偏度超过阈值: {sp_sum} > {pop_size/2}")
        # else:
        #     print(f"迭代 {iter+1} | 偏度小于阈值: {sp_sum} <= {pop_size/2}")
        
        # 打印每代的详细峰度计算信息
        print(f"\n迭代 {iter+1} | 峰度计算:")
        print(f"  - 适应度值: {current_fitness}")
        if verbose_kurtosis:
            print(f"  - 均值: {kurtosis_info['mean']:.4f}")
            print(f"  - 标准差: {kurtosis_info['std_dev']:.4f}")
            print(f"  - 四阶中心矩: {kurtosis_info['fourth_moment']:.4f}")
        print(f"  - 峰度值: {kurtosis_value:.4f}, 基准值: 3.0")
        print(f"  - 峰度状态: {'重尾分布' if kurtosis_value > 3 else '轻尾或正态分布'}")
        
        current_best = max(current_fitness)
        current_best_idx = current_fitness.index(current_best)
        
        # 更新全局最优解
        if current_best > best_fitness:
            best_fitness = current_best
            best_solution = X[current_best_idx].copy()
            print(f"迭代 {iter+1} | 最佳适应度: {best_fitness:.4f} (改进)")
        else:
            print(f"迭代 {iter+1} | 最佳适应度: {best_fitness:.4f}")
        
        # 更新适应度历史
        fitness_history.append(best_fitness)
    
    # 输出偏度统计摘要
    skewness_threshold_count = sum(1 for _, sp_sum, pop_size in skewness_stats if sp_sum > pop_size/2)
    print(f"\n偏度统计摘要:")
    print(f"偏度超过阈值的迭代次数: {skewness_threshold_count}/{max_iter} ({skewness_threshold_count/max_iter*100:.2f}%)")
    
    # 输出峰度统计摘要
    kurtosis_threshold_count = sum(1 for _, kurt in kurtosis_stats if kurt > 3)
    print(f"\n峰度统计摘要:")
    print(f"峰度大于3的迭代次数: {kurtosis_threshold_count}/{max_iter} ({kurtosis_threshold_count/max_iter*100:.2f}%)")
    
    return best_solution, fitness_history, skewness_details if verbose_skewness else None, kurtosis_details if verbose_kurtosis else None

# 主函数
def main():
    # 重置全局缓存
    global edv_cache
    edv_cache = {}
    
    # 加载网络
    network_path = "D:\\VS\\code\\networks\\netscience.txt"
    G = gen_graph(network_path)
    p = 0.05
    
    start_time = time.time()
    
    # 设置差分进化算法参数
    F = 0.6  # 变异因子
    CR = 0.4  # 交叉概率
    
    # 运行主算法
    best_seed, fitness_history, skewness_details, kurtosis_details = de(G, n=10, k=50, p=p, max_iter=100, F=F, CR=CR, verbose_skewness=True, verbose_kurtosis=True)
    
    # 评估最终结果
    ic_value = IC(G, best_seed, p, mc=1000)
    
    print(f"\n========== 最终结果 ==========")
    print(f"网络: {network_path}")
    print(f"种子集 (大小: {len(set(best_seed))}): {best_seed}")
    print(f"最终EDV值: {cached_edv(G, best_seed, p):.4f}")
    print(f"IC验证影响力: {ic_value:.4f}")
    print(f"运行时间: {time.time() - start_time:.2f} 秒")
    
    # 绘制峰度变化图
    plot_kurtosis(kurtosis_stats=[(i+1, calculate_kurtosis(skewness_details[i]["fitness_values"])) for i in range(len(skewness_details))])

if __name__ == "__main__":
    main()
