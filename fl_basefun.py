from unittest import result
import numpy as np
import networkx as nx
import random
import matplotlib.pyplot as plt
import math
from base_fun import IC, gen_graph, EDV, degree_initialization, local_search, visualize_seed_set, plot_fitness_history
import time
from collections import defaultdict
from evolution_trajectory import plot_evolution_on_landscape
from scipy import stats

# 全局缓存，避免重复计算EDV和节点评分
edv_cache = {}
node_score_cache = {}  # 添加全局节点评分缓存

# 包装EDV函数，使用全局缓存
def cached_edv(G, seed_set, p, k=None):
    """缓存版本的EDV，避免重复计算"""
    # 使用frozenset作为键可以提高哈希效率
    key = (frozenset(seed_set), p)
    if key not in edv_cache:
        edv_cache[key] = EDV(G, seed_set, p)
    return edv_cache[key]

# 计算和缓存单个节点的评分
def get_node_score(G, node, p):
    """获取单个节点的评分，使用全局缓存"""
    if node not in node_score_cache:
        node_score_cache[node] = EDV(G, [node], p)
    return node_score_cache[node]

# 定义一个可以被pickle的函数
def calculate_edv_for_subset(nodes, graph, p):
    """计算一组节点的EDV值"""
    return {n: EDV(graph, [n], p) for n in nodes}

# 初始化所有节点的评分
def initialize_node_scores(G, p):
    """预计算并缓存所有节点的影响力评分"""
    print("正在初始化所有节点评分...", end="")
    # 检查NetworkX版本是否支持并行计算
    try:
        import multiprocessing
        from functools import partial
        
        # 将节点分成多个批次
        all_nodes = list(G.nodes())
        num_cores = min(16, multiprocessing.cpu_count())
        chunk_size = max(1, len(all_nodes) // num_cores)
        node_chunks = [all_nodes[i:i+chunk_size] for i in range(0, len(all_nodes), chunk_size)]
        
        # 使用进程池并行计算
        with multiprocessing.Pool(processes=num_cores) as pool:
            results = pool.map(partial(calculate_edv_for_subset, graph=G, p=p), node_chunks)
        
        # 合并结果
        edv_scores = {}
        for result in results:
            edv_scores.update(result)
    except Exception as e:
        print(f"\n并行计算失败，切换到单线程计算: {e}")
        # 回退到串行计算
        edv_scores = {node: EDV(G, [node], p) for node in G.nodes()}
    
    node_score_cache.update(edv_scores)
    print(" 完成")

# 添加缓存清理函数，避免内存占用过大
def prune_cache(max_size=10000):
    """当缓存过大时清理不常用的项"""
    global edv_cache, node_score_cache
    
    # 清理EDV缓存
    if len(edv_cache) > max_size:
        # 只保留最近使用的一半缓存项
        items = list(edv_cache.items())
        edv_cache = dict(items[-max_size//2:])
    
    # 节点评分缓存通常较小，不需要清理
    # 但如果特别大，也可以考虑清理
    if len(node_score_cache) > max_size * 0.1:  # 如果超过阈值的10%
        print(f"Warning: node_score_cache has grown to {len(node_score_cache)} items")

# 计算两个解之间的汉明距离（用于度量解的差异性）
def calculate_distance(sol1, sol2):
    """计算两个解之间的汉明距离"""
    return len(set(sol1).symmetric_difference(set(sol2)))


# 修改后的局部搜索函数，不再限制评估的候选节点数量
def optimized_local_search(xi, G, p, k, max_iterations=1):
    """优化版本的局部搜索，使用高效的串行+批处理策略"""
    global node_score_cache
    
    xi_set = set(xi)
    xi_list = list(xi)
    xi_fitness = cached_edv(G, xi_list, p, k)

    improved = True
    iteration = 0
    
    # 使用全局节点评分缓存，不再需要重新计算
    
    # 预计算所有节点的邻居并缓存
    neighbor_cache = {}
    for node in G.nodes():
        neighbor_cache[node] = list(G.neighbors(node))
    
    while improved and iteration < max_iterations:
        improved = False
        iteration += 1
        
        # 采用贪婪策略更快地找到改进，先评估所有当前种子节点，找出贡献最小的节点
        node_contributions = []
        # for idx, node in enumerate(xi_list):
        #     # 计算节点贡献度 - 通过移除该节点观察影响
        #     temp_set = xi_set - {node}
        #     if temp_set:  # 确保不是空集
        #         removed_fitness = cached_edv(G, list(temp_set), p)
        #         contribution = xi_fitness - removed_fitness
        #         node_contributions.append((idx, node, contribution))
        #     else:
        #         # 如果只有一个节点，则使用节点自身评分
        #         node_contributions.append((idx, node, node_score_cache.get(node, 0)))

        # 直接使用节点评分作为贡献度
        for idx, node in enumerate(xi_list):
            node_contributions.append((idx, node, node_score_cache.get(node, 0)))

        # 按贡献度从低到高排序
        node_contributions.sort(key=lambda x: x[2])
        
        # 从贡献最小的节点开始尝试替换
        for idx, current_node, _ in node_contributions:
            # 获取当前节点的邻居
            neighbors = neighbor_cache.get(current_node, [])
            valid_neighbors = [n for n in neighbors if n not in xi_set]
            candidates = valid_neighbors

            # 按节点评分排序候选节点
            candidates.sort(key=lambda node: node_score_cache.get(node, 0), reverse=True)
            
            # 评估所有候选节点
            for candidate in candidates:
                # 创建包含交换后节点的新种子集
                new_xi = xi_list.copy()
                new_xi[idx] = candidate
                new_fitness = cached_edv(G, new_xi, p, k)
                improvement = new_fitness - xi_fitness
                
                if improvement > 0:
                    # 找到改进立即应用
                    xi_set.remove(current_node)
                    xi_set.add(candidate)
                    xi_list[idx] = candidate
                    xi_fitness = new_fitness
                    improved = True
                    
                    # 更新缓存
                    key = (frozenset(xi_list), p)
                    edv_cache[key] = xi_fitness
                    
                    # 立即返回第一个改进的解
                    return xi_list
    
    return xi_list

def calculate_kurtosis(fitness_values):
    """计算适应度序列的峰度，使用scipy.stats计算原始峰度（不减3）"""
    # 检查输入数据
    if len(fitness_values) <= 1:
        return 0, 0
    
    # 检查所有值是否相同
    if len(set(fitness_values)) == 1:
        # 所有值相同时返回数据量
        return len(fitness_values), len(fitness_values)
    
    # 使用scipy.stats中的kurtosis函数计算原始峰度
    # fisher=False表示计算原始峰度，不减去3
    # bias=False表示使用无偏估计
    result_unbiased = stats.kurtosis(fitness_values, fisher=False, bias=False)
    result_biased = stats.kurtosis(fitness_values, fisher=False, bias=True)
    return result_unbiased, result_biased

# 添加采样计算峰度阈值的函数
def calculate_kurtosis_threshold(G, k, p, num_samples=10000):
    """
    通过随机采样计算峰度阈值
    
    参数:
    G: 图对象
    k: 种子集大小
    p: 传播概率
    num_samples: 采样数量，默认10000
    
    返回:
    threshold: 建议的峰度阈值
    """
    print(f"开始计算峰度阈值")
    # 节点列表
    nodelist = list(G.nodes())
    n = len(nodelist)
    
    # 用于存储采样解的适应度
    fitness_samples = []
    
    # 随机采样
    for i in range(num_samples):
            
        # 随机生成一个种子集
        sample = random.sample(nodelist, k)
        # 计算适应度
        fitness = EDV(G, sample, p)
        fitness_samples.append(fitness)
    
    # 使用scipy.stats计算采样数据的峰度
    sample_kurtosis = stats.kurtosis(fitness_samples, fisher=True) + 3  # 加3转换为总体峰度
    print(f"采样次数: {num_samples},随机采样峰度值: {sample_kurtosis:.4f}")
    return sample_kurtosis


