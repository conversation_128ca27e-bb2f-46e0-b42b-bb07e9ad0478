import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.cm as cm
from matplotlib.font_manager import FontProperties
from sklearn.manifold import MDS
from scipy.interpolate import griddata
import matplotlib.colors as mcolors
from scipy.ndimage import gaussian_filter
from matplotlib.patches import Patch
import random
from collections import defaultdict

def setup_chinese_font():
    """设置中文字体环境，确保所有图表元素都能正确显示中文"""
    import matplotlib as mpl
    
    # 设置字体路径
    font_path = r"C:\Windows\Fonts\msyh.ttc"
    
    # 获取FontProperties对象
    font = FontProperties(fname=font_path, size=12)
    
    # 设置全局字体
    plt.rcParams['font.family'] = ['Microsoft YaHei', 'SimHei', 'sans-serif']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 如果需要，可以更详细地配置Matplotlib的字体
    mpl.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei']
    
    return font

def plot_evolution_on_landscape(iterations_data, interval=10, show_iteration_colorbar=False, replacement_data=None, save_path=None):
    """
    在适应度地形上绘制进化轨迹，强化轨迹连接
    
    参数:
    iterations_data: 包含每次迭代数据的列表
    interval: 取样间隔，每隔多少代绘制一次
    show_iteration_colorbar: 是否显示迭代次数颜色条
    replacement_data: 节点替换数据，包含替换前后的个体信息
    save_path: 保存图像的路径，如果为None则使用默认路径
    """
    # 定义节点替换的固定颜色 - 移到函数开头
    SUCCESS_COLOR = '#00CC00'  # 亮绿色
    FAILURE_COLOR = '#CC0000'  # 亮红色
    
    # 设置中文字体为微软雅黑
    font = FontProperties(fname=r"C:\Windows\Fonts\msyh.ttc", size=12)
    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
    
    # 收集所有个体和适应度值
    all_individuals = []
    all_fitness = []
    sampled_iterations = []
    
    # 为了轨迹连接，单独收集每次迭代的最佳个体
    best_individuals = []
    best_fitness_values = []
    best_iteration_nums = []
    best_pts_fitness = []
    
    for i, data in enumerate(iterations_data):
        if i % interval == 0 or i == len(iterations_data) - 1:
            # 获取当前迭代的个体和适应度
            individuals = data.get('individuals', [])
            fitness_values = data['fitness']
            
            # 如果没有个体数据但有距离数据，跳过
            if not individuals and 'distances' in data:
                continue
                
            # 记录所有个体用于地形图
            all_individuals.extend(individuals)
            all_fitness.extend(fitness_values)
            sampled_iterations.extend([i] * len(individuals))
            
            # 记录最佳个体用于轨迹连接
            if fitness_values:
                best_idx = np.argmax(fitness_values)
                best_individuals.append(individuals[best_idx])
                best_fitness_values.append(fitness_values[best_idx])
                best_iteration_nums.append(i)
                best_pts_fitness.append(fitness_values[best_idx])
    
    # 如果有替换数据，添加到all_individuals中
    replacement_pairs = []
    if replacement_data:
        # 将替换数据分为成功和失败两组
        successful_replacements = []
        failed_replacements = []
        
        for rep_info in replacement_data:
            old_ind = rep_info['old_individual']
            new_ind = rep_info['new_individual']
            old_fitness = rep_info['old_fitness']
            new_fitness = rep_info['actual_new_fitness']
            iteration = rep_info['iteration']
            
            # 依据适应度变化判断是成功还是失败
            if new_fitness > old_fitness:
                successful_replacements.append(rep_info)
            else:
                failed_replacements.append(rep_info)
        
        print(f"总共有 {len(successful_replacements)} 个成功替换和 {len(failed_replacements)} 个失败替换")
        
        # 平衡成功和失败替换的数量 - 确保两者显示数量相同
        max_to_show = 10  # 每类最多显示的替换数量
        
        # 随机选择成功替换
        if successful_replacements:
            num_successful = min(max_to_show, max(1, int(len(successful_replacements) * 0.1)))
            selected_successful = random.sample(successful_replacements, min(num_successful, len(successful_replacements)))
            print(f"选择了 {len(selected_successful)} 个成功替换进行显示")
        else:
            selected_successful = []
            print("没有成功的替换可供选择")
        
        # 随机选择失败替换 - 如果成功替换有了数量，我们平衡失败替换的数量
        if failed_replacements:
            if selected_successful:
                # 平衡数量：使失败替换与成功替换数量相同
                num_failed = len(selected_successful)
            else:
                # 如果没有成功替换，仍然显示一些失败替换
                num_failed = min(max_to_show, max(1, int(len(failed_replacements) * 0.1)))
                
            selected_failed = random.sample(failed_replacements, min(num_failed, len(failed_replacements)))
            print(f"选择了 {len(selected_failed)} 个失败替换进行显示")
        else:
            selected_failed = []
            print("没有失败的替换可供选择")
        
        # 合并选中的替换
        selected_replacements = selected_successful + selected_failed
        print(f"总共将显示 {len(selected_replacements)} 个替换箭头")
        
        # 添加到总体数据
        for rep_info in selected_replacements:
            old_ind = rep_info['old_individual']
            new_ind = rep_info['new_individual']
            old_fitness = rep_info['old_fitness']
            new_fitness = rep_info['actual_new_fitness']
            iteration = rep_info['iteration']
            
            # 添加到总体数据中
            all_individuals.append(old_ind)
            all_individuals.append(new_ind)
            all_fitness.append(old_fitness)
            all_fitness.append(new_fitness)
            sampled_iterations.extend([iteration, iteration])
            
            # 记录替换对
            replacement_pairs.append((len(all_individuals)-2, len(all_individuals)-1, iteration))
    
    if not all_individuals:
        print("没有足够的数据来绘制适应度地形图")
        return
        
    # 计算所有个体间的距离矩阵
    n = len(all_individuals)
    distance_matrix = np.zeros((n, n))
    for i in range(n):
        for j in range(i+1, n):
            ind1 = set(all_individuals[i])
            ind2 = set(all_individuals[j])
            distance = len(ind1.symmetric_difference(ind2))
            distance_matrix[i, j] = distance
            distance_matrix[j, i] = distance
    
    # 使用多维尺度分析(MDS)将个体映射到2D空间
    mds = MDS(n_components=2, dissimilarity='precomputed', random_state=42)
    points_2d = mds.fit_transform(distance_matrix)
    
    # 创建用于绘制地形的网格
    x_min, x_max = points_2d[:, 0].min() - 1, points_2d[:, 0].max() + 1
    y_min, y_max = points_2d[:, 1].min() - 1, points_2d[:, 1].max() + 1
    grid_x, grid_y = np.mgrid[x_min:x_max:100j, y_min:y_max:100j]
    
    # 使用插值生成适应度曲面
    # 将 method 从 'cubic' 改为 'linear' 或 'nearest' 以减少平滑效果
    grid_z = griddata(points_2d, all_fitness, (grid_x, grid_y), method='linear', fill_value=np.min(all_fitness))
    
    # 可选：减少或移除高斯滤波
    # 如果之前应用了高斯滤波，可以减小 sigma 值或完全移除
    # grid_z = gaussian_filter(grid_z, sigma=0.5)  # 减小 sigma 值或注释掉此行
    
    # 创建图形
    fig = plt.figure(figsize=(16, 12))
    
    # 3D曲面图
    ax1 = fig.add_subplot(121, projection='3d')
    surf = ax1.plot_surface(grid_x, grid_y, grid_z, cmap=cm.viridis, alpha=1, 
                          linewidth=0, antialiased=True)
    
    # 按迭代次数设置点的颜色
    unique_iterations = sorted(set(sampled_iterations))
    norm = plt.Normalize(min(unique_iterations), max(unique_iterations))
    colors = plt.cm.plasma(norm(sampled_iterations))
    
    # 在3D图上绘制所有个体
    scatter = ax1.scatter(points_2d[:, 0], points_2d[:, 1], all_fitness, c=colors, s=30, alpha=1)
    
    # 获取最佳个体在映射空间中的坐标和适应度
    best_pts_indices = []
    best_pts_coords = []
    
    # 找出最佳个体在points_2d中的索引位置
    for best_ind in best_individuals:
        for i, ind in enumerate(all_individuals):
            if set(best_ind) == set(ind):  # 使用集合比较避免顺序问题
                best_pts_indices.append(i)
                best_pts_coords.append((points_2d[i, 0], points_2d[i, 1]))
                break
    
    # 在3D图上绘制最佳个体轨迹，使用渐变色和粗线
    if len(best_pts_indices) > 1:
        # 计算轨迹线的颜色渐变
        cmap = plt.cm.plasma
        trajectory_colors = cmap(np.linspace(0, 1, len(best_pts_indices)-1))
        
        # 绘制轨迹线
        for i in range(len(best_pts_indices)-1):
            idx1, idx2 = best_pts_indices[i], best_pts_indices[i+1]
            ax1.plot([points_2d[idx1, 0], points_2d[idx2, 0]], 
                    [points_2d[idx1, 1], points_2d[idx2, 1]],
                    [all_fitness[idx1], all_fitness[idx2]], 
                    color=trajectory_colors[i], linewidth=3, alpha=1)
        
        # 在3D图上单独绘制最佳个体
        ax1.scatter([p[0] for p in best_pts_coords], 
                   [p[1] for p in best_pts_coords], 
                   best_pts_fitness, 
                   c=plt.cm.plasma(norm(best_iteration_nums)), 
                   s=80, alpha=1.0, marker='*', edgecolor='black')
    
    # 添加颜色条显示迭代次数（根据参数决定是否显示）
    if show_iteration_colorbar:
        cbar1 = fig.colorbar(scatter, ax=ax1, shrink=0.6, aspect=10)
        cbar1.set_label('迭代次数', fontproperties=font)
    
    # 设置标签
    ax1.set_xlabel('降维坐标X', fontproperties=font)
    ax1.set_ylabel('降维坐标Y', fontproperties=font)
    ax1.set_zlabel('适应度', fontproperties=font)
    ax1.set_title('进化轨迹与适应度地形', fontproperties=font)
    
    # 等高线图
    ax2 = fig.add_subplot(122)
    contour = ax2.contourf(grid_x, grid_y, grid_z, 50, cmap=cm.viridis, alpha=0.8)

    # 为每个个体分配一个唯一的颜色，彻底避开红色和绿色及其接近色
    n_individuals = len(iterations_data[0]['individuals'])

    # 创建一个精心挑选的颜色列表，彻底避开红色和绿色及其接近色
    safe_colors = [
        '#1f77b4',  # 蓝色
        '#9467bd',  # 紫色
        '#8c564b',  # 棕色
        '#e377c2',  # 粉色
        '#7f7f7f',  # 灰色
        '#17becf',  # 青色
        '#aec7e8',  # 浅蓝色
        '#c5b0d5',  # 浅紫色
        '#c49c94',  # 浅棕色
        '#f7b6d2',  # 浅粉色
        '#9edae5',  # 浅青色
        '#393b79',  # 深蓝色
        '#6b6ecf',  # 靛蓝色
        '#5254a3',  # 深紫色
        '#637939',  # 深橄榄色
        '#3182bd',  # 中蓝色
        '#6baed6',  # 天蓝色
        '#9ecae1',  # 淡蓝色
        '#756bb1',  # 中紫色
        '#8a89a6'   # 灰紫色
    ]

    # 确保有足够的颜色
    if n_individuals > len(safe_colors):
        # 如果个体数量超过预定义颜色，使用HSV色彩空间生成更多颜色
        import colorsys
        
        def generate_safe_colors(n):
            # 生成更多颜色，但严格避开红色和绿色区域
            colors = []
            hue_step = 1.0 / n
            
            # 定义要避开的色相范围
            # 红色区域: 约0.95-0.05
            # 绿色区域: 约0.25-0.40
            red_range = [(0.95, 1.0), (0.0, 0.05)]
            green_range = [(0.25, 0.40)]
            orange_range = [(0.05, 0.15)]  # 橙色也接近红色
            yellow_green_range = [(0.15, 0.25)]  # 黄绿色接近绿色
            
            # 生成n个颜色
            count = 0
            hue = 0.0
            while len(colors) < n and count < n*3:  # 设置上限避免无限循环
                count += 1
                
                # 检查当前色相是否在禁用范围内
                in_forbidden_range = False
                for r_min, r_max in red_range:
                    if r_min <= hue <= r_max:
                        in_forbidden_range = True
                        break
                
                for r_min, r_max in green_range:
                    if r_min <= hue <= r_max:
                        in_forbidden_range = True
                        break
                        
                for r_min, r_max in orange_range:
                    if r_min <= hue <= r_max:
                        in_forbidden_range = True
                        break
                        
                for r_min, r_max in yellow_green_range:
                    if r_min <= hue <= r_max:
                        in_forbidden_range = True
                        break
                
                # 如果不在禁用范围，添加这个颜色
                if not in_forbidden_range:
                    rgb = colorsys.hsv_to_rgb(hue, 0.8, 0.9)
                    colors.append(rgb)
                
                # 移动到下一个色相
                hue = (hue + hue_step) % 1.0
            
            return colors
        
        additional_colors = generate_safe_colors(n_individuals - len(safe_colors))
        # 将RGB元组转换为十六进制颜色代码
        additional_hex_colors = ['#{:02x}{:02x}{:02x}'.format(int(r*255), int(g*255), int(b*255)) 
                                for r, g, b in additional_colors]
        safe_colors.extend(additional_hex_colors)

    # 使用安全颜色列表
    individual_colors = safe_colors[:n_individuals]

    # 创建一个字典来存储每个个体的所有点
    individual_points = defaultdict(list)

    # 收集每个个体在不同迭代中的点
    for i, data in enumerate(iterations_data):
        if i % interval == 0 or i == len(iterations_data) - 1:
            individuals = data.get('individuals', [])
            for j, ind in enumerate(individuals):
                # 使用集合表示来识别相同的个体
                ind_set = frozenset(ind)
                # 找到这个个体在points_2d中的索引
                for k, all_ind in enumerate(all_individuals):
                    if frozenset(all_ind) == ind_set and sampled_iterations[k] == i:
                        individual_points[j].append((points_2d[k, 0], points_2d[k, 1], all_fitness[k], i))
                        break

    # 先绘制节点替换箭头，确保它们不会被散点遮挡
    if replacement_pairs:
        print(f"绘制 {len(replacement_pairs)} 个替换箭头")
        for old_idx, new_idx, iter_num in replacement_pairs:
            # 箭头起点和终点
            x1, y1 = points_2d[old_idx, 0], points_2d[old_idx, 1]
            x2, y2 = points_2d[new_idx, 0], points_2d[new_idx, 1]
            
            # 计算适应度变化
            fitness_change = all_fitness[new_idx] - all_fitness[old_idx]
            
            # 使用固定的红色和绿色
            arrow_color = SUCCESS_COLOR if fitness_change > 0 else FAILURE_COLOR
            
            # 绘制带箭头的线段
            ax2.annotate('', xy=(x2, y2), xytext=(x1, y1),
                        arrowprops=dict(arrowstyle='->',
                                      lw=3,
                                      color=arrow_color,
                                      alpha=1.0,
                                      mutation_scale=20,
                                      shrinkA=0, shrinkB=0))
            
            # 在3D图上也绘制替换箭头
            ax1.plot([x1, x2], [y1, y2], [all_fitness[old_idx], all_fitness[new_idx]], 
                    color=arrow_color, linewidth=3, alpha=1.0, linestyle='-')
            
            # 打印调试信息
            print(f"绘制替换箭头: ({x1:.2f}, {y1:.2f}) -> ({x2:.2f}, {y2:.2f}), 颜色: {arrow_color}")

    # 随机选择5个个体进行绘制，但确保包含最终最优解的个体
    selected_individuals = []
    if individual_points:
        # 首先确定哪个个体是最终最优解
        final_best_individual_idx = None
        if best_individuals:
            final_best_ind = best_individuals[-1]
            # 查找这个个体在original_individuals中的索引
            for j in individual_points.keys():
                for i, data in enumerate(iterations_data):
                    if i == len(iterations_data) - 1:  # 最后一次迭代
                        individuals = data.get('individuals', [])
                        if j < len(individuals) and set(individuals[j]) == set(final_best_ind):
                            final_best_individual_idx = j
                            break
                if final_best_individual_idx is not None:
                    break
            
            # 如果找到了最终最优解对应的个体索引，先将其添加到选中列表
            if final_best_individual_idx is not None and final_best_individual_idx in individual_points:
                selected_individuals.append(final_best_individual_idx)
                print(f"已添加最终最优解（个体 {final_best_individual_idx+1}）到绘制列表")
        
        # 获取所有有轨迹的个体索引（排除已选的最优解）
        available_individuals = [j for j in individual_points.keys() 
                                if len(individual_points[j]) > 1 and j not in selected_individuals]
        
        # 随机选择剩余的个体，最多选到总共5个
        # remaining_to_select = min(5 - len(selected_individuals), len(available_individuals))
        remaining_to_select = 1
        if remaining_to_select > 0:
            additional_individuals = random.sample(available_individuals, remaining_to_select)
            selected_individuals.extend(additional_individuals)
            print(f"随机选择了额外 {len(additional_individuals)} 个个体进行轨迹绘制")
        
        print(f"总共将绘制 {len(selected_individuals)} 个个体的轨迹: {selected_individuals}")
    else:
        print("没有足够的个体轨迹可供选择")

    # 为选定的个体绘制散点和轨迹
    for j in selected_individuals:
        if individual_points[j]:
            x_coords = [p[0] for p in individual_points[j]]
            y_coords = [p[1] for p in individual_points[j]]
            fitness = [p[2] for p in individual_points[j]]
            iterations = [p[3] for p in individual_points[j]]
            
            # 判断是否为最终最优解
            is_final_best = (j == final_best_individual_idx)
            
            # 绘制该个体的所有点
            ax2.scatter(x_coords, y_coords, 
                       color=individual_colors[j % len(individual_colors)], 
                       s=50, alpha=0.8, 
                       label=f'个体 {j+1}{" (最优解)" if is_final_best else ""}')
            
            # 如果有多个点，连接它们以显示轨迹
            if len(x_coords) > 1:
                for i in range(len(x_coords)-1):
                    ax2.annotate('', xy=(x_coords[i+1], y_coords[i+1]), 
                                xytext=(x_coords[i], y_coords[i]),
                                arrowprops=dict(arrowstyle='->', 
                                              lw=1.5, 
                                              color=individual_colors[j % len(individual_colors)], 
                                              alpha=0.6,
                                              mutation_scale=10,
                                              shrinkA=0, shrinkB=0))
                                              
            # 如果是最终最优解，在最后一个点添加特殊标记
            if is_final_best and x_coords:
                # 在轨迹的最后一个点（最优解）添加星形标记
                ax2.scatter(x_coords[-1], y_coords[-1], 
                           s=200, 
                           color=individual_colors[j % len(individual_colors)],
                           marker='*', 
                           edgecolor='black', 
                           linewidth=1.5,
                           zorder=10,  # 确保绘制在最上层
                           label='最终最优解')

    # 添加图例
    handles = []
    labels = []

    # 个体图例 - 只为选定的个体添加图例
    for j in selected_individuals:
        is_final_best = (j == final_best_individual_idx)
        handles.append(plt.Line2D([0], [0], marker='o', color='w', 
                                 markerfacecolor=individual_colors[j % len(individual_colors)], 
                                 markersize=8))
        labels.append(f'个体 {j+1}{" (最优解)" if is_final_best else ""}')
    
    # 如果找到了最终最优解，添加一个特殊的星形标记到图例
    if final_best_individual_idx is not None and final_best_individual_idx in selected_individuals:
        handles.append(plt.Line2D([0], [0], marker='*', color='w',
                                 markerfacecolor=individual_colors[final_best_individual_idx % len(individual_colors)],
                                 markersize=12, markeredgecolor='black', markeredgewidth=1.5))
        labels.append('最终最优解位置')

    # 替换箭头图例
    if replacement_pairs:
        handles.append(plt.Line2D([0], [0], color=SUCCESS_COLOR, lw=2, linestyle='-'))
        labels.append('成功替换 (适应度提升)')
        handles.append(plt.Line2D([0], [0], color=FAILURE_COLOR, lw=2, linestyle='-'))
        labels.append('失败替换 (适应度下降)')

    # 添加图例 - 放在图外边
    legend = ax2.legend(handles, labels, 
                      loc='upper left',  # 图例左上角的位置点
                      bbox_to_anchor=(1.05, 1.0),  # 定位点相对于图的位置：(1.05, 1.0)表示在图的右侧
                      ncol=1,  # 图例的列数
                      borderaxespad=0.,  # 轴与图例边缘的间距
                      frameon=True,  # 是否显示图例边框
                      prop=font)
    
    # 调整图例背景为半透明
    if legend:
        legend.get_frame().set_alpha(0.8)
    
    # 添加颜色条
    cbar2 = fig.colorbar(contour, ax=ax2, shrink=0.6, aspect=10)
    cbar2.set_label('适应度值', fontproperties=font)
    
    # 设置标签
    ax2.set_xlabel('降维坐标X', fontproperties=font)
    ax2.set_ylabel('降维坐标Y', fontproperties=font)
    ax2.set_title('进化轨迹在适应度地形上的投影', fontproperties=font)
    
    # 添加轨迹渐变色图例
    if len(best_pts_indices) > 1:
        # 创建轨迹颜色图例
        sm = plt.cm.ScalarMappable(cmap=plt.cm.plasma, norm=plt.Normalize(min(best_iteration_nums), max(best_iteration_nums)))
        sm.set_array([])
        cbar_traj = fig.colorbar(sm, ax=ax2, shrink=0.6, aspect=10, pad=0.15, location='right')
        cbar_traj.set_label('进化轨迹迭代次数', fontproperties=font)
        
        # 修改这部分：每10代显示一次刻度，而不是全部显示
        tick_interval = 10
        # 计算10的倍数的刻度位置
        tick_values = []
        tick_labels = []
        for i, iter_num in enumerate(best_iteration_nums):
            if iter_num % tick_interval == 0 or iter_num == best_iteration_nums[-1]:
                tick_values.append(iter_num)
                tick_labels.append(str(iter_num))
                
        cbar_traj.set_ticks(tick_values)
        cbar_traj.set_ticklabels(tick_labels)
    
    # 调整图形布局，确保图例不会被裁剪
    # 替换tight_layout()，它可能无法正确处理图外的图例
    plt.subplots_adjust(right=0.85)  # 为图例留出右侧空间
    
    # 如果没有提供保存路径，使用默认路径
    if save_path is None:
        save_path = 'evolution_landscape.pdf'
    
    try:
        print(f"正在保存图像到: {save_path}")
        # 确保保存时包含图例
        plt.savefig(save_path, format='pdf', dpi=300, bbox_inches='tight')
        print(f"图像已成功保存到: {save_path}")
    except Exception as e:
        print(f"保存图像时出错: {e}")
    
    plt.show()


