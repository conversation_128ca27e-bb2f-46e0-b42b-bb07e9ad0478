import matplotlib.pyplot as plt
import matplotlib as mpl
from matplotlib.gridspec import GridSpec

# 添加绘制峰度图的函数
def plot_kurtosis(kurtosis_stats, title="峰度值变化趋势", kurtosis_threshold=3):
    """
    绘制峰度值随迭代次数变化的折线图
    
    参数:
    kurtosis_stats: 一个列表，包含(迭代次数, 峰度值)的元组
    title: 图表标题
    kurtosis_threshold: 峰度阈值，默认为3
    """
    try:
        import matplotlib.pyplot as plt
        import matplotlib as mpl
        
        # 设置中文字体为微软雅黑
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']  # 指定默认字体为微软雅黑
        plt.rcParams['axes.unicode_minus'] = False  # 解决保存图像时负号'-'显示为方块的问题
        
        # 提取迭代次数和峰度值
        iterations = [item[0] for item in kurtosis_stats]
        kurtosis_values = [item[1] for item in kurtosis_stats]
        
        # 创建图表
        plt.figure(figsize=(10, 6))
        plt.plot(iterations, kurtosis_values, 'b-', linewidth=2, marker='o')
        
        # 添加基准线（使用计算的阈值而不是固定值3）
        plt.axhline(y=kurtosis_threshold, color='r', linestyle='--', alpha=0.7, label=f'峰度阈值 (K={kurtosis_threshold:.4f})')
        
        # 添加标题和标签
        plt.title(title, fontsize=15)
        plt.xlabel('迭代次数', fontsize=12)
        plt.ylabel('峰度值', fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.legend()
        
        # 标记峰度>阈值的点
        for i, k in enumerate(kurtosis_values):
            if k > kurtosis_threshold:
                plt.plot(iterations[i], k, 'ro', markersize=8)
        
        # 保存图表到当前文件夹
        filename = 'kurtosis_evolution_plot.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"峰度变化图已保存至: {filename}")
        
        # 不显示图表，直接关闭
        plt.close()
        
    except Exception as e:
        print(f"绘制峰度图时出错: {e}")

# 添加新的函数，将适应度历史和峰度合并到一个图表中
def plot_fitness_and_kurtosis(fitness_history, kurtosis_stats, save_path='fitness_and_kurtosis.png', kurtosis_threshold=3):
    """
    将适应度历史和峰度值变化合并在一个双Y轴图表中展示
    
    参数:
    fitness_history: 适应度历史记录列表
    kurtosis_stats: 一个列表，包含(迭代次数, 峰度值)的元组
    save_path: 保存图片的路径
    kurtosis_threshold: 峰度阈值
    """
    try:
        import matplotlib.pyplot as plt
        import matplotlib as mpl
        from matplotlib.gridspec import GridSpec
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']  # 指定默认字体为微软雅黑
        plt.rcParams['axes.unicode_minus'] = False  # 解决保存图像时负号'-'显示为方块的问题
        
        # 提取峰度数据
        iterations = [item[0] for item in kurtosis_stats]
        kurtosis_values = [item[1] for item in kurtosis_stats]
        
        # 创建一个高质量的图表
        plt.figure(figsize=(12, 8), dpi=100, facecolor='white')
        gs = GridSpec(1, 1)
        ax1 = plt.subplot(gs[0, 0])
        
        # 设置第一个Y轴：适应度历史
        color1 = '#1f77b4'  # 蓝色
        line1, = ax1.plot(range(len(fitness_history)), fitness_history, 
                       color=color1, linewidth=2.5, marker='o', 
                       markerfacecolor=color1, markeredgecolor='white',
                       markersize=6, label='最佳适应度')
        
        ax1.set_xlabel('迭代次数', fontsize=14, fontweight='bold')
        ax1.set_ylabel('适应度值', fontsize=14, fontweight='bold', color=color1)
        ax1.tick_params(axis='y', labelcolor=color1, labelsize=12)
        ax1.tick_params(axis='x', labelsize=12)
        ax1.grid(True, linestyle='--', alpha=0.3, which='both')
        
        # 标记最佳适应度点
        best_idx = fitness_history.index(max(fitness_history))
        ax1.scatter([best_idx], [fitness_history[best_idx]], color='green', s=150, 
                     label=f'最佳适应度: {fitness_history[best_idx]:.4f} (迭代: {best_idx})')
        
        # 设置第二个Y轴：峰度值
        ax2 = ax1.twinx()
        color2 = '#ff7f0e'  # 橙色
        
        # 绘制峰度值曲线
        line2, = ax2.plot(iterations, kurtosis_values, 
                       color=color2, linewidth=2, linestyle='-', marker='s', 
                       markerfacecolor=color2, markeredgecolor='white',
                       markersize=5, label='峰度值')
        
        # 添加基准线（使用计算出的峰度阈值而非固定值3）
        line3 = ax2.axhline(y=kurtosis_threshold, color='red', linestyle='--', alpha=0.7, 
                           label=f'峰度阈值 (K={kurtosis_threshold:.4f})')
        
        # 标记峰度>阈值的点
        above_threshold_x = []
        above_threshold_y = []
        for i, k in enumerate(kurtosis_values):
            if k > kurtosis_threshold:
                above_threshold_x.append(iterations[i])
                above_threshold_y.append(k)
        
        scatter = ax2.scatter(above_threshold_x, above_threshold_y, color='red', s=80, 
                        label='峰度值 > 阈值', zorder=5)
        
        ax2.set_ylabel('峰度值', fontsize=14, fontweight='bold', color=color2)
        ax2.tick_params(axis='y', labelcolor=color2, labelsize=12)
        
        # 设置图例
        lines = [line1, line2, line3, scatter]
        labels = [l.get_label() for l in lines]
        ax1.legend(lines, labels, loc='upper center', fontsize=12, 
                  bbox_to_anchor=(0.5, -0.15), ncol=2, frameon=True, 
                  facecolor='white', edgecolor='gray')
        
        # 设置标题
        plt.title('适应度与峰度变化趋势对比', fontsize=16, fontweight='bold', pad=15)
        
        # 添加边框
        for spine in ax1.spines.values():
            spine.set_linewidth(1.5)
        
        # 调整布局并保存
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"适应度与峰度变化图已保存至: {save_path}")
        plt.close()
        
    except Exception as e:
        print(f"绘制适应度与峰度图时出错: {e}")
        import traceback
        traceback.print_exc()