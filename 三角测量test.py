import math

# 定义向量坐标
A_vec = [1, 2, 3, 10, 11, 14, 15]
G_vec = [1, 2, 3, 4, 5, 7, 9]

# 计算点积
dot_product = sum(a*g for a,g in zip(A_vec, G_vec))

# 计算向量模长
A_magnitude = math.sqrt(sum(a**2 for a in A_vec))
G_magnitude = math.sqrt(sum(g**2 for g in G_vec))

# 计算余弦相似度
cosine_similarity = dot_product / (A_magnitude * G_magnitude)

# 显示结果
print(f"向量A: {A_vec}")
print(f"向量G: {G_vec}")
print(f"点积 A·G: {dot_product}")
print(f"|A| = {A_magnitude}")
print(f"|G| = {G_magnitude}")
print(f"余弦相似度 cos(A,G) = {cosine_similarity}")
print(f"夹角 θ = {round(math.degrees(math.acos(cosine_similarity)), 2)}°")

import math

# 请帮我修改陷入局部最优个体的引导节点数
# 假定G为当前全局最优，A为陷入局部最优的个体，提取A和G差异的节点，按节点评分依次排序，如下，A和G为全局最优G和陷入局部最优个体两个差异的节点数评分
A_score = [8, 9, 10, 11, 12, 14, 15]
G_score = [1, 2, 3, 4, 5, 6, 7]
# 之后计算A和G的余弦，对于A的引导节点数为m=d/cos(A,G)，就是将A中m个评分最低的节点用网络中评分最高的m个节点替换。
# 计算点积
dot_product = sum(a*g for a,g in zip(A_vec, G_vec))

# 计算向量模长
A_magnitude = math.sqrt(sum(a**2 for a in A_vec))
G_magnitude = math.sqrt(sum(g**2 for g in G_vec))

# 计算余弦相似度
cosine_similarity = dot_product / (A_magnitude * G_magnitude)

# 显示结果
print(f"向量A: {A_vec}")
print(f"向量G: {G_vec}")
print(f"点积 A·G: {dot_product}")
print(f"|A| = {A_magnitude}")
print(f"|G| = {G_magnitude}")
print(f"余弦相似度 cos(A,G) = {cosine_similarity}")
print(f"夹角 θ = {round(math.degrees(math.acos(cosine_similarity)), 2)}°")