from unittest import result
import numpy as np
import networkx as nx
import random
import matplotlib.pyplot as plt
import math
from base_fun import IC, gen_graph, EDV, degree_initialization, local_search, visualize_seed_set, plot_fitness_history
import time
from collections import defaultdict
from scipy import stats
import multiprocessing
from functools import partial

# 全局缓存，避免重复计算EDV和节点评分
edv_cache = {}
node_score_cache = {}  # 添加全局节点评分缓存

# 包装EDV函数，使用全局缓存
def cached_edv(G, seed_set, p, k=None):
    """缓存版本的EDV，避免重复计算"""
    # 使用frozenset作为键可以提高哈希效率
    key = (frozenset(seed_set), p)
    if key not in edv_cache:
        edv_cache[key] = EDV(G, seed_set, p)
    return edv_cache[key]

# 计算和缓存单个节点的评分
def get_node_score(G, node, p):
    """获取单个节点的评分，使用全局缓存"""
    if node not in node_score_cache:
        node_score_cache[node] = EDV(G, [node], p)
    return node_score_cache[node]

# 定义一个可以被pickle的函数
def calculate_edv_for_subset(nodes, graph, p):
    """计算一组节点的EDV值"""
    return {n: EDV(graph, [n], p) for n in nodes}

# 初始化所有节点的评分
def initialize_node_scores(G, p, method="edv"):
    """
    预计算并缓存所有节点的评分

    参数:
    G: 图对象
    p: 传播概率
    method: 评分方法，可选值为"edv"(默认)或"degree"
    """
    if method == "degree":
        print("正在初始化所有节点度中心性评分...", end="")
        # 直接使用NetworkX的度中心性计算函数
        degree_scores = nx.degree_centrality(G)
        # 更新全局缓存
        node_score_cache.update(degree_scores)
        print(f" 完成，共计算了 {len(degree_scores)} 个节点的度中心性")
        return

    # 默认使用EDV评分
    print("正在初始化所有节点EDV评分...", end="")
    # 检查NetworkX版本是否支持并行计算
    try:
        import multiprocessing
        from functools import partial

        # 将节点分成多个批次
        all_nodes = list(G.nodes())
        num_cores = min(16, multiprocessing.cpu_count())
        chunk_size = max(1, len(all_nodes) // num_cores)
        node_chunks = [all_nodes[i:i+chunk_size] for i in range(0, len(all_nodes), chunk_size)]

        # 使用进程池并行计算
        with multiprocessing.Pool(processes=num_cores) as pool:
            results = pool.map(partial(calculate_edv_for_subset, graph=G, p=p), node_chunks)

        # 合并结果
        edv_scores = {}
        for result in results:
            edv_scores.update(result)
    except Exception as e:
        print(f"\n并行计算失败，切换到单线程计算: {e}")
        # 回退到串行计算
        edv_scores = {node: EDV(G, [node], p) for node in G.nodes()}

    node_score_cache.update(edv_scores)
    print(f" 完成，共计算了 {len(edv_scores)} 个节点的EDV评分")

# 添加缓存清理函数，避免内存占用过大
def prune_cache(max_size=10000):
    """
    当缓存过大时清理不常用的项

    参数:
    max_size: 缓存的最大大小，如果为0则完全清空缓存
    """
    global edv_cache, node_score_cache

    # 如果max_size为0，完全清空缓存
    if max_size == 0:
        edv_cache.clear()
        node_score_cache.clear()
        print("已清空所有缓存")
        return

    # 清理EDV缓存
    if len(edv_cache) > max_size:
        # 只保留最近使用的一半缓存项
        items = list(edv_cache.items())
        edv_cache = dict(items[-max_size//2:])
        print(f"已清理EDV缓存至 {len(edv_cache)} 项")

    # 节点评分缓存通常较小，不需要清理
    # 但如果特别大，也可以考虑清理
    if len(node_score_cache) > max_size * 0.1:  # 如果超过阈值的10%
        print(f"Warning: node_score_cache has grown to {len(node_score_cache)} items")


# 个体停滞时的智能替换策略
def intelligent_node_replacement(G, individual, best_solution, p):
    """
    对陷入停滞状态的个体进行智能节点替换，并对新添加的节点进行一阶邻域搜索优化

    参数:
    G: 图对象
    individual: 当前个体
    best_solution: 当前最优解
    p: 传播概率

    返回:
    new_individual: 替换后的新个体
    actual_replaced_count: 替换的节点数量
    baseline_fitness: 新个体的适应度值
    """
    global node_score_cache, edv_cache

    ind_set = set(individual)
    best_set = set(best_solution)
    hamming_distance = len(ind_set.symmetric_difference(best_set)) / 2  # 对称差集的一半为单个集合差异节点个数

    # 确保替换汉明距离大小个节点，但至少替换1个，避免无效替换
    m = max(1, int(hamming_distance))
    k = len(best_set)
    if m == k:
        m = k - 1  # 最多替换k-1个节点，避免替换为度中心性

    # 创建新个体的副本
    new_individual = individual.copy()

    # 找出个体中贡献度最低的m个节点
    node_contributions = []

    # 先计算一次当前个体的适应度，避免重复计算
    current_fitness = cached_edv(G, list(ind_set), p)

    # 获取当前个体和最优个体的共同节点
    common_nodes = ind_set.intersection(best_set)

    for node in individual:
        # 如果节点在最优个体中也存在，给它一个非常高的贡献度值，确保它不会被替换
        if node in common_nodes:
            # 使用一个非常大的值作为贡献度，确保这些节点不会被选为替换对象
            # node_contributions.append((node, float('inf')))
            node_contributions.append((node, len(G.nodes())+1))
        else:
            # 只计算那些不在最优个体中的节点的贡献度
            temp_set = ind_set - {node}
            if temp_set:  # 确保不是空集
                # 使用缓存计算移除节点后的适应度
                removed_fitness = cached_edv(G, list(temp_set), p)
                contribution = current_fitness - removed_fitness
                node_contributions.append((node, contribution))
            else:
                # 如果只有一个节点，则使用节点自身评分
                node_contributions.append((node, node_score_cache.get(node, 0)))

    # 按贡献度从低到高排序
    node_contributions.sort(key=lambda x: x[1])
    lowest_contributing_nodes = [node for node, _ in node_contributions[:m]]

    # 找出不在个体中评分最高的节点
    candidates = [(node, node_score_cache.get(node, 0))
                 for node in G.nodes() if node not in ind_set]
    candidates.sort(key=lambda x: x[1], reverse=True)  # 按评分从高到低排序
    highest_scoring_candidates = [node for node, _ in candidates[:m]]

    # 替换节点并跟踪新添加的节点
    newly_added_nodes = []
    actual_replaced_count = 0
    for i in range(min(len(lowest_contributing_nodes), len(highest_scoring_candidates))):
        remove_node = lowest_contributing_nodes[i]
        add_node = highest_scoring_candidates[i]

        # 在新个体中进行替换
        new_individual.remove(remove_node)
        new_individual.append(add_node)
        newly_added_nodes.append(add_node)
        actual_replaced_count += 1

    # 计算替换后的基准适应度
    baseline_fitness = cached_edv(G, new_individual, p)
    new_individual_set = set(new_individual)

    # 二次方向微调，对于新加入的节点探索一阶邻居
    for added_node in newly_added_nodes:
        # 获取该节点的一阶邻居
        neighbors = list(G.neighbors(added_node))
        valid_neighbors = [n for n in neighbors if n not in new_individual_set]

        if not valid_neighbors:
            continue  # 如果没有有效邻居，继续下一个节点

        # 按节点评分排序邻居节点
        valid_neighbors.sort(key=lambda n: node_score_cache.get(n, 0), reverse=True)

        # 尝试用所有邻居替换当前新增节点
        for neighbor in valid_neighbors:
            # 创建临时解
            temp_solution = new_individual.copy()
            temp_solution.remove(added_node)
            temp_solution.append(neighbor)

            # 计算新解的适应度
            temp_fitness = cached_edv(G, temp_solution, p)

            # 如果找到更好的解，更新
            if temp_fitness > baseline_fitness:
                new_individual = temp_solution
                new_individual_set = set(new_individual)
                baseline_fitness = temp_fitness
                break  # 找到改进就立即应用并继续下一个节点

    # 计算实际替换的节点数量
    old_set = set(individual)
    new_set = set(new_individual)
    actual_replaced_count = len(old_set.symmetric_difference(new_set)) // 2  # 对称差集的一半为实际替换的节点数量

    return new_individual, actual_replaced_count, baseline_fitness

# 更新个体停滞检测状态，并在检测到停滞时进行智能节点替换
def update_stagnation_detection(i, current_iter, fitness_history, fitness_current,
                              stagnation_counters, stagnation_states, individuals, best_solution, G, p,
                              stagnation_threshold=0, stagnation_trigger=3, is_generation_best=False):
    """
    更新个体停滞检测状态，并在检测到停滞时进行智能节点替换

    参数:
    i: 个体索引
    current_iter: 当前迭代次数
    fitness_history: 适应度历史记录
    fitness_current: 当前适应度
    stagnation_counters: 停滞计数器
    stagnation_states: 停滞状态
    individuals: 当前种群
    best_solution: 当前最优解
    G: 图对象
    p: 传播概率
    stagnation_threshold: 停滞阈值
    stagnation_trigger: 触发停滞的连续代数
    is_generation_best: 是否为当前代最优个体

    返回:
    (是否从停滞状态中恢复, 是否进行了节点替换, 新个体, 替换信息, 新适应度)
    """
    # 更新适应度历史记录(保持最近10代)
    fitness_history[i].append(fitness_current)
    if len(fitness_history[i]) > 10:
        fitness_history[i] = fitness_history[i][-10:]

    recovered = False
    replaced = False
    new_individual = individuals[i].copy()
    replacement_info = None
    new_fitness = fitness_current  # 默认使用当前适应度

    # 至少需要两代才能计算变化
    if len(fitness_history[i]) < 2:
        return recovered, replaced, new_individual, replacement_info, new_fitness

    # 计算适应度变化
    fitness_change = fitness_history[i][-1] - fitness_history[i][-2]
    abs_change = abs(fitness_change)

    # 情况1: 适应度显著提升 - 重置停滞状态
    if fitness_change > stagnation_threshold:
        if stagnation_states[i]:  # 如果之前处于停滞状态
            recovered = True
        stagnation_counters[i] = 0
        stagnation_states[i] = False

    # 情况2: 适应度停止变化,可能处于停滞状态
    elif abs_change < stagnation_threshold:
        stagnation_counters[i] += 1

        # 连续3代无显著变化，判定为停滞
        if stagnation_counters[i] >= stagnation_trigger:
            # 更新停滞状态标记
            stagnation_states[i] = True

            # 对非最优个体执行替换操作
            if not is_generation_best:
                old_individual = individuals[i].copy()
                old_fitness = fitness_current
                new_individual, num_replaced, new_fitness = intelligent_node_replacement(G, individuals[i], best_solution, p)

                # 再次计算新个体的实际适应度，确保与缓存一致
                new_fitness = cached_edv(G, new_individual, p)

                replaced = True
                stagnation_counters[i] = 0

                # 记录替换信息
                replacement_info = {
                    'iteration': current_iter,
                    'individual_idx': i,
                    'old_individual': old_individual,
                    'new_individual': new_individual,
                    'old_fitness': old_fitness,
                    'new_fitness': new_fitness,
                    'num_replaced': num_replaced
                }

    return recovered, replaced, new_individual, replacement_info, new_fitness


# 修改后的局部搜索函数，不再限制评估的候选节点数量
def optimized_local_search(xi, G, p, k, max_iterations=1):
    """优化版本的局部搜索，使用高效的串行+批处理策略"""
    global node_score_cache

    xi_set = set(xi)
    xi_list = list(xi)
    xi_fitness = cached_edv(G, xi_list, p, k)

    improved = True
    iteration = 0

    # 使用全局节点评分缓存，不再需要重新计算

    # 预计算所有节点的邻居并缓存
    neighbor_cache = {}
    for node in G.nodes():
        neighbor_cache[node] = list(G.neighbors(node))

    while improved and iteration < max_iterations:
        improved = False
        iteration += 1

        # 采用贪婪策略更快地找到改进，先评估所有当前种子节点，找出贡献最小的节点
        node_contributions = []
        # for idx, node in enumerate(xi_list):
        #     # 计算节点贡献度 - 通过移除该节点观察影响
        #     temp_set = xi_set - {node}
        #     if temp_set:  # 确保不是空集
        #         removed_fitness = cached_edv(G, list(temp_set), p)
        #         contribution = xi_fitness - removed_fitness
        #         node_contributions.append((idx, node, contribution))
        #     else:
        #         # 如果只有一个节点，则使用节点自身评分
        #         node_contributions.append((idx, node, node_score_cache.get(node, 0)))

        # 直接使用节点评分作为贡献度
        for idx, node in enumerate(xi_list):
            node_contributions.append((idx, node, node_score_cache.get(node, 0)))

        # 按贡献度从低到高排序
        node_contributions.sort(key=lambda x: x[2])

        # 从贡献最小的节点开始尝试替换
        for idx, current_node, _ in node_contributions:
            # 获取当前节点的邻居
            neighbors = neighbor_cache.get(current_node, [])
            valid_neighbors = [n for n in neighbors if n not in xi_set]
            candidates = valid_neighbors

            # 按节点评分排序候选节点
            candidates.sort(key=lambda node: node_score_cache.get(node, 0), reverse=True)

            # 评估所有候选节点
            for candidate in candidates:
                # 创建包含交换后节点的新种子集
                new_xi = xi_list.copy()
                new_xi[idx] = candidate
                new_fitness = cached_edv(G, new_xi, p, k)
                improvement = new_fitness - xi_fitness

                if improvement > 0:
                    # 找到改进立即应用
                    xi_set.remove(current_node)
                    xi_set.add(candidate)
                    xi_list[idx] = candidate
                    xi_fitness = new_fitness
                    improved = True

                    # 更新缓存
                    key = (frozenset(xi_list), p)
                    edv_cache[key] = xi_fitness

                    # 立即返回第一个改进的解
                    return xi_list

    return xi_list

def calculate_kurtosis(fitness_values):
    """计算适应度序列的峰度，使用scipy.stats计算原始峰度（不减3）"""
    # 检查输入数据
    if len(fitness_values) <= 1:
        return 0, 0

    # 检查所有值是否相同
    if len(set(fitness_values)) == 1:
        # 所有值相同时返回数据量
        return len(fitness_values), len(fitness_values)

    # 使用scipy.stats中的kurtosis函数计算原始峰度
    # fisher=False表示计算原始峰度，不减去3
    # bias=False表示使用无偏估计
    result_unbiased = stats.kurtosis(fitness_values, fisher=False, bias=False)
    result_biased = stats.kurtosis(fitness_values, fisher=False, bias=True)
    return result_unbiased, result_biased

