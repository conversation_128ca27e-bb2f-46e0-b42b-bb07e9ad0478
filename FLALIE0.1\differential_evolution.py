"""
差分进化算法模块，包含变异、交叉、选择和主算法函数
"""
import numpy as np
import random
import math
import time
from collections import defaultdict
from fl_basefun import (edv_cache, node_score_cache, cached_edv, 
                       intelligent_node_replacement, update_stagnation_detection,
                       optimized_local_search, calculate_kurtosis, initialize_node_scores)
from base_fun import EDV, degree_initialization


# 优化的差分变异操作
def differential_mutation(G, X, F, pop, k, p=0.1, terrain="ridge", best_solution=None):
    """
    优化的差分变异操作
    
    参数:
    G: 图对象
    X: 当前种群
    F: 变异因子
    pop: 种群大小
    k: 种子集大小
    p: 传播概率
    terrain: 地形类型，可选值为"ridge"或"peak"
    best_solution: 当前最优解
    
    返回:
    M: 变异后的种群
    """
    global node_score_cache
    nodes_list = list(G.nodes())  # 只创建一次
    M = []  # 变异种群结果容器
    X_sets = [frozenset(x) for x in X]

    # 对每个个体执行变异操作
    for i in range(pop):
        # 当前解
        Xcurrent = X[i].copy()

        # === 基于地形特征的搜索策略选择 ===
        if terrain == "ridge":
            # 使用random.sample代替多次random.choice，减少随机数生成开销 #DE/rand/1/bin
            rand_indices = random.sample(range(pop), min(3, pop))
            base_vector = X[rand_indices[0]].copy()
            diff_set = X_sets[rand_indices[1]] - X_sets[rand_indices[2]]
        else:
            # "peak" 山峰区#DE/current-to-rand/1/bin 变体
            rand_indices = random.sample(range(pop), min(3, pop))
            base_vector = Xcurrent.copy()
            diff_set1 = X_sets[rand_indices[0]] - frozenset(Xcurrent)
            diff_set2 = X_sets[rand_indices[1]] - X_sets[rand_indices[2]]
            diff_set = diff_set1.union(diff_set2)

        M.append(base_vector)
        N = math.ceil(F * len(diff_set))
        current_set = set(M[i])
        node_to_score = {node: node_score_cache.get(node, 0) for node in current_set}

        for _ in range(N):
            if not current_set:
                break
            # 找出最差节点 - 避免每次都遍历整个集合
            worst_node = min(current_set, key=lambda node: node_score_cache.get(node, 0))
            # 转换为列表以加速随机选择
            available_diff = list(diff_set - current_set)

            if available_diff:
                replace_node = random.choice(available_diff)
            else:
                # 预先计算可用节点集合
                available_nodes = list(set(nodes_list) - current_set)
                if not available_nodes:
                    continue
                replace_node = random.choice(available_nodes)
            # 执行替换 - 优化异常处理逻辑
            try:
                worst_index = M[i].index(worst_node)
                M[i][worst_index] = replace_node
                current_set.remove(worst_node)
                current_set.add(replace_node)
                # 更新节点评分字典
                node_to_score.pop(worst_node, None)
                node_to_score[replace_node] = node_score_cache.get(replace_node, 0)
            except ValueError:
                # 简化异常处理逻辑
                M[i] = list((current_set - {worst_node}) | {replace_node})
                while len(M[i]) < k:
                    candidate = random.choice(list(set(nodes_list) - set(M[i])))
                    M[i].append(candidate)
                # 重建当前集合
                current_set = set(M[i])

    return M


# 优化的交叉操作
def crossover(X, M, cr, pop, k, nodelist):
    """
    优化版交叉操作
    
    参数:
    X: 当前种群
    M: 变异后的种群
    cr: 交叉概率
    pop: 种群大小
    k: 种子集大小
    nodelist: 节点列表
    
    返回:
    real_C: 交叉后的种群
    """
    nodelist_set = set(nodelist)  # 预先创建节点集合以加速操作
    real_C = []

    # 获取节点评分（如果存在）
    node_scores = getattr(differential_mutation, 'node_scores', {})

    # 预生成随机数列表，减少函数调用开销
    random_values = [random.random() for _ in range(pop * k)]

    for i in range(pop):
        # 创建跟踪集合和结果列表
        added_nodes = set()
        C = []

        for j in range(k):
            ran = random_values[i * k + j]

            # 简化逻辑判断，减少重复计算
            use_m = (ran < cr and M[i][j] not in added_nodes)
            use_x = not use_m and X[i][j] not in added_nodes

            if use_m:
                temp = M[i][j]
            elif use_x:
                temp = X[i][j]
            else:
                # 冲突处理 - 直接从未添加节点中选择
                available = nodelist_set - added_nodes
                temp = random.choice(list(available)) if available else random.choice(nodelist)

            C.append(temp)
            added_nodes.add(temp)

        # 高效补充缺失节点
        if len(C) < k:
            # 使用集合差集直接找出未使用的节点
            available_nodes = list(nodelist_set - added_nodes)

            # 如果有节点评分，则使用评分排序
            if node_scores:
                available_nodes.sort(key=lambda n: node_scores.get(n, 0), reverse=True)

            # 填充剩余位置
            C.extend(available_nodes[:k-len(C)])

        real_C.append(C)

    return real_C


# 优化的选择操作
def selection(G, X, C, pop, p=0.1):
    """
    优化的选择操作
    
    参数:
    G: 图对象
    X: 当前种群
    C: 交叉后的种群
    pop: 种群大小
    p: 传播概率
    
    返回:
    temp_X: 选择后的种群
    """
    X_keys = [frozenset(x) for x in X]
    C_keys = [frozenset(c) for c in C]
    solutions_to_evaluate = set(X_keys + C_keys)
    for sol in solutions_to_evaluate:
        if (sol, p) not in edv_cache:
            edv_cache[(sol, p)] = EDV(G, list(sol), p)
    temp_X = []
    for i in range(pop):
        x_fitness = edv_cache[(X_keys[i], p)]
        c_fitness = edv_cache[(C_keys[i], p)]

        temp_X.append(X[i] if x_fitness >= c_fitness else C[i])

    return temp_X


# 优化的差分进化算法主函数
def de(G, n, k, p, max_iter=50, F=0.6, cr=0.4, kurtosis_threshold=3):
    """
    优化的差分进化算法主函数
    
    参数:
    G: 图对象
    n: 种群大小
    k: 种子集大小
    p: 传播概率
    max_iter: 最大迭代次数
    F: 变异因子
    cr: 交叉概率
    kurtosis_threshold: 峰度阈值
    
    返回:
    best_solution: 最优解
    fitness_history: 适应度历史
    evolution_data: 进化数据
    replacement_data: 节点替换数据
    kurtosis_stats: 峰度统计
    """
    global node_score_cache
    initialize_node_scores(G, p, method="degree")

    nodelist = list(G.nodes())
    fitness_history = []

    # 地形统计 - 只统计切换为山峰的次数
    terrain_transitions = {"to_peak": 0}
    # 局部搜索统计
    local_search_stats = {"total_attempts": 0, "successful_attempts": 0}
    # 节点替换统计
    replacement_stats = {"total_attempts": 0, "successful_attempts": 0, "exceeded_global_best": 0}

    # 修改停滞检测的初始化
    individual_fitness_history = [[] for _ in range(n)]  # 每个个体的适应度历史
    stagnation_detected = [False] * n  # 记录每个个体是否处于停滞状态
    stagnation_counter = [0] * n  # 记录每个个体连续停滞的代数

    # 峰度计算相关数据结构
    kurtosis_stats = []  # 存储每代的峰度值
    kurtosis_history = []  # 记录所有代的峰度值

    # 初始化种群
    X = degree_initialization(G, n, k)

    # 预计算初始种群适应度
    unique_solutions = {tuple(sorted(ind)) for ind in X}
    for sol in unique_solutions:
        edv_cache[sol] = EDV(G, list(sol), p)

    # 获取当前最佳解
    population_fitness = [cached_edv(G, ind, p) for ind in X]
    current_best = max(population_fitness)
    current_best_idx = population_fitness.index(current_best)

    best_solution = X[current_best_idx].copy()
    best_fitness = current_best
    fitness_history.append(best_fitness)

    # 初始化地形 - 默认设为山脊
    current_terrain = "ridge"
    # 进化控制变量
    last_fitness = best_fitness

    # 初始化进化轨迹数据收集
    evolution_data = []
    replacement_data = []  # 新增：记录节点替换数据

    # 主进化循环
    for iter in range(max_iter):
        # 评估当前种群
        current_fitness = [cached_edv(G, ind, p) for ind in X]

        # 计算当前种群的峰度值
        result_unbiased, result_biased = calculate_kurtosis(current_fitness)
        print(f"峰度值 (unbiased): {result_unbiased:.4f}, 峰度值 (biased): {result_biased:.4f}")
        kurtosis_value = result_unbiased

        kurtosis_stats.append((iter+1, kurtosis_value))
        kurtosis_history.append(kurtosis_value)

        # 使用计算的峰度阈值替代固定值3
        if kurtosis_value > kurtosis_threshold:
            print(f"第{iter+1}代 | 激活地形感知策略 (峰度值 {kurtosis_value:.4f} > {kurtosis_threshold:.4f})")
            previous_terrain = current_terrain

            # 计算所有个体的适应度并找出最优个体
            individual_fitness = [(i, cached_edv(G, ind, p)) for i, ind in enumerate(X)]
            individual_fitness.sort(key=lambda x: x[1], reverse=True)
            best_individual_idx = individual_fitness[0][0]
            best_individual = X[best_individual_idx].copy()

            # 对最优个体执行局部搜索
            start_time = time.time()
            optimized = optimized_local_search(best_individual, G, p, k)
            print(f"局部搜索完成，用时: {time.time() - start_time:.2f} 秒")
            original_fitness = cached_edv(G, best_individual, p)
            new_fitness = cached_edv(G, optimized, p)

            # 更新局部搜索统计
            local_search_stats["total_attempts"] += 1

            # 根据局部搜索结果判断地形
            if new_fitness > original_fitness:
                # 局部搜索有改进，处于山峰区
                local_search_stats["successful_attempts"] += 1

                # 只有当从非山峰区切换到山峰区时才记录
                if current_terrain != "peak":
                    terrain_transitions["to_peak"] += 1
                    print(f"地形从{current_terrain}切换为peak")

                current_terrain = "peak"
                X[best_individual_idx] = optimized  # 使用优化后的个体替换
                current_fitness[best_individual_idx] = new_fitness  # 更新适应度
            else:
                print(f"^^^^^^^^^^^^^陷入局部最优峰值，执行引导策略^^^^^^^^^^^^^")
                # 无论之前是什么地形，都设置为ridge
                current_terrain = "ridge"

                # 执行引导策略,计算每个个体与最优解的汉明距离，对停滞个体执行节点替换
                best_solution_set = set(best_solution)
                for i in range(n):
                    # 跳过最优个体,只对非最优个体执行节点替换
                    if i == best_individual_idx:
                        continue

                    # 执行智能节点替换
                    old_fitness = cached_edv(G, X[i], p)
                    old_individual = X[i].copy()  # 保存替换前的个体
                    new_individual, num_replaced, new_fitness = intelligent_node_replacement(G, X[i], best_solution, p)

                    # 再次计算新个体的实际适应度，确保与缓存一致
                    new_fitness = cached_edv(G, new_individual, p)

                    # 更新节点替换统计
                    replacement_stats["total_attempts"] += 1
                    if new_fitness > old_fitness:
                        replacement_stats["successful_attempts"] += 1

                    # 检查是否超过全局最优解
                    if new_fitness > best_fitness:
                        replacement_stats["exceeded_global_best"] += 1

                    X[i] = new_individual
                    current_fitness[i] = new_fitness  # 更新当前适应度
                    stagnation_counter[i] = 0  # 重置停滞计数器

                    # 记录替换信息
                    replacement_info = { 'iteration': iter+1, 'individual_idx': i, 'old_individual': old_individual,
                        'new_individual': new_individual, 'old_fitness': old_fitness, 'new_fitness': new_fitness,
                        'actual_new_fitness': new_fitness, 'num_replaced': num_replaced, 'is_best_in_generation': False
                    }
                    replacement_data.append(replacement_info)

        else:
            print(f"第{iter+1}代 | 正常进化 (峰度值 {kurtosis_value:.4f} < {kurtosis_threshold:.4f})")
            # 峰度值低于阈值时，始终使用ridge地形
            current_terrain = "ridge"

        # 根据当前地形执行差分变异
        M = differential_mutation(G, X, F, n, k, p=p, terrain=current_terrain)
        C = crossover(X, M, cr, n, k, nodelist)
        X = selection(G, X, C, n, p)

        # 评估当前种群
        current_fitness = [cached_edv(G, ind, p) for ind in X]
        current_best = max(current_fitness)
        best_idx = current_fitness.index(current_best)

        # 更新全局最优解
        if current_best > best_fitness:
            best_fitness = current_best
            best_solution = X[best_idx].copy()

        # 更新适应度历史
        fitness_history.append(current_best)
        # 更新每个个体的适应度历史并检查停滞
        recovered_count = 0
        # 找出当前代的最优个体索引
        current_best_idx = np.argmax(current_fitness)

        # 更新每个个体的适应度历史并检查停滞
        for i in range(n):
            # 判断当前个体是否为本代最优
            is_best_in_generation = (i == current_best_idx)
            recovered, replaced, new_individual, replacement_info, new_fitness = update_stagnation_detection(
                i, iter+1, individual_fitness_history, current_fitness[i],
                stagnation_counter, stagnation_detected, X, best_solution, G, p,
                stagnation_threshold=1e-6, stagnation_trigger=3, #停滞阈值设为1e-6
                is_generation_best=is_best_in_generation  # 传入是否为当前代最优
            )

            # 如果进行了节点替换，更新个体和适应度
            if replaced:
                old_fitness = current_fitness[i]
                X[i] = new_individual
                # 使用函数返回的适应度，不再重新计算
                current_fitness[i] = new_fitness

                # 更新节点替换统计
                replacement_stats["total_attempts"] += 1
                if new_fitness > old_fitness:
                    replacement_stats["successful_attempts"] += 1

                # 检查是否超过全局最优解
                if new_fitness > best_fitness:
                    replacement_stats["exceeded_global_best"] += 1

                if replacement_info:
                    replacement_info['actual_new_fitness'] = new_fitness  # 使用新计算的适应度
                    replacement_info['is_best_in_generation'] = is_best_in_generation
                    # 检查是否超过全局最优解
                    replacement_info['exceeded_global_best'] = new_fitness > best_fitness
                    replacement_data.append(replacement_info)

            if recovered:
                recovered_count += 1


        # 更新全局最优解(如果有改进)
        generation_best_idx = np.argmax(current_fitness)
        generation_best = current_fitness[generation_best_idx]

        if generation_best > current_best:
            current_best = generation_best
            best_solution = X[generation_best_idx].copy()

        # 在所有替换和进化操作完成后，再收集当前代的数据
        current_iteration_data = {
            'individuals': X.copy(),
            'fitness': current_fitness.copy(),  # 确保使用最新的适应度值
            'terrain': current_terrain
        }
        evolution_data.append(current_iteration_data)

        print(f"当前最佳适应度: {current_best:.4f}, 平均适应度: {np.mean(current_fitness):.4f}")
        print(f"最优个体: {best_solution}")
        print('=============================================================')

    # 计算局部搜索成功率
    local_search_success_rate = 0
    if local_search_stats["total_attempts"] > 0:
        local_search_success_rate = local_search_stats["successful_attempts"] / local_search_stats["total_attempts"] * 100

    # 计算节点替换成功率
    replacement_success_rate = 0
    if replacement_stats["total_attempts"] > 0:
        replacement_success_rate = replacement_stats["successful_attempts"] / replacement_stats["total_attempts"] * 100

    # 输出局部搜索统计
    print(f"==================局部搜索统计 ==================")
    print(f"总尝试次数: {local_search_stats['total_attempts']}，成功次数: {local_search_stats['successful_attempts']}")
    print(f"成功率: {local_search_success_rate:.2f}%")

    # 输出节点替换统计
    print(f"==================节点替换统计==================")
    print(f"总尝试次数: {replacement_stats['total_attempts']}，成功次数: {replacement_stats['successful_attempts']}")
    print(f"成功率: {replacement_success_rate:.2f}%")
    print(f"超过全局最优次数: {replacement_stats['exceeded_global_best']}")

    # 输出地形转换统计
    print(f"==================地形转换统计==================")
    print(f"切换为山峰区次数: {terrain_transitions['to_peak']}次")

    # 输出峰度统计摘要
    kurtosis_threshold_count = sum(1 for _, kurt in kurtosis_stats if kurt > kurtosis_threshold)
    print(f"==================峰度统计摘要==================")
    print(f"峰度大于{kurtosis_threshold:.0f}的迭代次数: {kurtosis_threshold_count}/{max_iter} ({kurtosis_threshold_count/max_iter*100:.2f}%)")

    return best_solution, fitness_history, evolution_data, replacement_data, kurtosis_stats
