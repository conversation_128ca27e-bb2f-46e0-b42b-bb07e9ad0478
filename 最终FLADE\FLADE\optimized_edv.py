import numpy as np
import networkx as nx
from numba import jit
from functools import lru_cache
from typing import List, Set, Tuple, Dict
import scipy.sparse as sp

class EDVCalculator:
    def __init__(self, graph: nx.Graph):
        """初始化 EDV 计算器，预计算并缓存必要的数据结构"""
        self.graph = graph
        self.nodes = list(graph.nodes())
        self.node_to_idx = {node: idx for idx, node in enumerate(self.nodes)}
        self.n = len(self.nodes)

        # 预计算并缓存邻接矩阵（CSR格式）
        # print("预计算邻接矩阵...")
        self.adj_csr = nx.adjacency_matrix(graph, nodelist=self.nodes).astype(bool).tocsr()

        # 预计算节点的一跳邻居
        # print("预计算节点邻居...")
        self._precompute_neighbors()

    def _precompute_neighbors(self):
        """预计算并缓存每个节点的邻居"""
        self.neighbors_dict = {}
        for node in self.nodes:
            idx = self.node_to_idx[node]
            # 使用CSR矩阵的行切片获取邻居 - 修复1D切片问题
            start, end = self.adj_csr.indptr[idx], self.adj_csr.indptr[idx + 1]
            neighbors = self.adj_csr.indices[start:end]
            self.neighbors_dict[node] = set(self.nodes[i] for i in neighbors)

    @staticmethod
    @jit(nopython=True)
    def _calculate_influence(adj_data: np.ndarray, adj_indices: np.ndarray,
                           adj_indptr: np.ndarray, seed_indices: np.ndarray,
                           n: int, p: float) -> float:
        """使用numba加速的影响力计算核心函数"""
        # 初始化连接计数数组
        conn_counts = np.zeros(n, dtype=np.int32)

        # 计算每个节点与种子节点的连接数
        for seed_idx in seed_indices:
            start, end = adj_indptr[seed_idx], adj_indptr[seed_idx + 1]
            for j in range(start, end):
                if adj_data[j]:
                    conn_counts[adj_indices[j]] += 1

        # 计算影响力
        influence = 0.0
        for count in conn_counts:
            if count > 0:
                influence += 1.0 - (1.0 - p) ** count

        return influence

    def calculate_edv(self, seed_set: Set[int], p: float) -> float:
        """计算给定种子集的EDV值"""
        if not seed_set:
            return 0.0

        # 转换种子节点为索引
        seed_indices = np.array([self.node_to_idx[s] for s in seed_set if s in self.node_to_idx],
                              dtype=np.int32)

        if len(seed_indices) == 0:
            return 0.0

        # 调用numba加速的核心计算函数
        influence = self._calculate_influence(
            self.adj_csr.data.astype(bool),
            self.adj_csr.indices,
            self.adj_csr.indptr,
            seed_indices,
            self.n,
            p
        )

        return influence

class CachedEDVCalculator:
    def __init__(self, graph: nx.Graph, cache_size: int = 10000, neighbors_cache=None):
        """
        初始化带缓存的EDV计算器

        参数:
        graph: NetworkX图对象
        cache_size: 缓存大小
        neighbors_cache: 预计算的邻居缓存 {节点: [邻居列表]}
        """
        self.graph = graph
        self.neighbors_cache = neighbors_cache
        self.calculator = EDVCalculator(graph)
        self.cache_size = cache_size
        self._initialize_cache()

    def _initialize_cache(self):
        """初始化LRU缓存装饰器"""
        @lru_cache(maxsize=self.cache_size)
        def cached_edv(seed_set_key: Tuple[int, ...], p: float) -> float:
            seed_set = set(seed_set_key)
            return self.calculator.calculate_edv(seed_set, p)

        self._cached_edv_impl = cached_edv

    def calculate_edv(self, seed_set: Set[int], p: float) -> float:
        """计算EDV值，优先使用缓存"""
        # 将集合转换为排序后的元组，用作缓存键
        seed_key = tuple(sorted(seed_set))
        return self._cached_edv_impl(seed_key, p)

    def clear_cache(self):
        """清除缓存"""
        self._cached_edv_impl.cache_clear()

    def cache_info(self):
        """获取缓存统计信息"""
        return self._cached_edv_impl.cache_info()