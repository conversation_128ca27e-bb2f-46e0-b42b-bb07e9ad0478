
import numpy as np
import networkx as nx
import random
import matplotlib.pyplot as plt
import math
import time
import pandas as pd
import openpyxl
from collections import defaultdict
from scipy import stats
from datetime import datetime
from base_fun import (IC, gen_graph,EDV, degree_initialization, local_search, visualize_seed_set, plot_fitness_history
)
# 直接导入GEN_GRAPH类
from base_fun import GEN_GRAPH
from optimized_edv import CachedEDVCalculator
from evolution_trajectory import plot_evolution_on_landscape
from plot import plot_fitness_and_kurtosis
from output import export_replacement_data_to_excel, export_evolution_stats_to_excel
from flbasefun import (edv_cache, node_score_cache, get_node_score, cached_edv, initialize_node_scores,
                      prune_cache, calculate_distance, optimized_local_search, calculate_kurtosis,
                      calculate_kurtosis_threshold, intelligent_node_replacement, update_stagnation_detection,
                      optimized_edv_with_cache)
from differential_evolution import Differential_Mutation, Crossover, Selection

# 优化的差分进化算法主函数
def de(G, n, k, p, max_iter=50, F=0.6, cr=0.4, kurtosis_threshold=3, neighbors_cache=None):
    # 初始化EDV计算器，传入邻居缓存
    edv_calculator = CachedEDVCalculator(G, neighbors_cache=neighbors_cache)
    # print("EDV计算器初始化完成（使用预计算邻居缓存），开始优化...")

    global node_score_cache
    initialize_node_scores(G, p)

    nodelist = list(G.nodes())
    fitness_history = []

    # 地形统计 - 只统计切换为山峰的次数
    terrain_transitions = {"to_peak": 0}
    # 局部搜索统计
    local_search_stats = {"total_attempts": 0, "successful_attempts": 0}
    # 节点替换统计
    replacement_stats = {"total_attempts": 0, "successful_attempts": 0, "exceeded_global_best": 0}

    # 修改停滞检测的初始化
    individual_fitness_history = [[] for _ in range(n)]  # 每个个体的适应度历史
    stagnation_detected = [False] * n  # 记录每个个体是否处于停滞状态
    stagnation_counter = [0] * n  # 记录每个个体连续停滞的代数

    # 峰度计算相关数据结构
    kurtosis_stats = []  # 存储每代的峰度值
    kurtosis_history = []  # 记录所有代的峰度值

    # 初始化种群
    X = degree_initialization(G, n, k)

    # 预计算初始种群适应度
    unique_solutions = {tuple(sorted(ind)) for ind in X}
    for sol in unique_solutions:
        # 使用带邻居缓存的EDV计算
        edv_cache[sol] = optimized_edv_with_cache(G, list(sol), p, neighbors_cache) if neighbors_cache else EDV(G, list(sol), p)

    # 获取当前最佳解
    population_fitness = [cached_edv(G, ind, p, neighbors_cache=neighbors_cache) for ind in X]
    current_best = max(population_fitness)
    current_best_idx = population_fitness.index(current_best)

    best_solution = X[current_best_idx].copy()
    best_fitness = current_best
    fitness_history.append(best_fitness)

    # 初始化地形 - 默认设为山脊
    current_terrain = "ridge"
    # 进化控制变量
    last_fitness = best_fitness

    # 初始化进化轨迹数据收集
    evolution_data = []
    replacement_data = []  # 新增：记录节点替换数据

    # 主进化循环
    # for iter in range(max_iter):
    from tqdm import tqdm
    for iter in tqdm(range(max_iter), desc="进化迭代", ncols=100):
        # 评估当前种群，传入邻居缓存
        current_fitness = [cached_edv(G, ind, p, neighbors_cache=neighbors_cache) for ind in X]

        # 计算当前种群的峰度值
        kurtosis_value = calculate_kurtosis(current_fitness)
        # print(f"峰度值: {kurtosis_value:.4f}")

        kurtosis_stats.append((iter+1, kurtosis_value))
        kurtosis_history.append(kurtosis_value)

        # 使用计算的峰度阈值替代固定值3
        if kurtosis_value > kurtosis_threshold:
            # print(f"第{iter+1}代 | 激活地形感知策略 (峰度值 {kurtosis_value:.4f} > {kurtosis_threshold:.4f})")

            # 计算所有个体的适应度并找出最优个体
            individual_fitness = [(i, fitness) for i, fitness in enumerate(current_fitness)]
            individual_fitness.sort(key=lambda x: x[1], reverse=True)
            best_individual_idx = individual_fitness[0][0]
            best_individual = X[best_individual_idx].copy()

            # 对最优个体执行局部搜索，传入邻居缓存
            # start_time = time.time()
            optimized = optimized_local_search(best_individual, G, p, k, neighbors_cache=neighbors_cache)
            # print(f"局部搜索完成，用时: {time.time() - start_time:.2f} 秒")
            original_fitness = cached_edv(G, best_individual, p, neighbors_cache=neighbors_cache)
            new_fitness = cached_edv(G, optimized, p, neighbors_cache=neighbors_cache)

            # 更新局部搜索统计
            local_search_stats["total_attempts"] += 1

            # 根据局部搜索结果判断地形
            if new_fitness > original_fitness:
                # 局部搜索有改进，处于山峰区
                local_search_stats["successful_attempts"] += 1

                # 只有当从非山峰区切换到山峰区时才记录
                if current_terrain != "peak":
                    terrain_transitions["to_peak"] += 1
                    # print(f"地形从{current_terrain}切换为peak")

                current_terrain = "peak"
                X[best_individual_idx] = optimized  # 使用优化后的个体替换
                current_fitness[best_individual_idx] = new_fitness  # 更新适应度
            else:
                # print(f"^^^^^^^^^^^^^陷入局部最优峰值，执行引导策略^^^^^^^^^^^^^")
                # 无论之前是什么地形，都设置为ridge
                current_terrain = "ridge"

                # 找出当前种群中的最优个体索引
                current_best_idx = np.argmax(current_fitness)

                # 执行引导策略,计算每个个体与最优解的汉明距离，对停滞个体执行节点替换
                for i in range(n):
                    # print(f"代”{iter+1} | 个体{i} | 节点替换")
                    # 跳过最优个体,只对非最优个体执行节点替换
                    if i == current_best_idx:
                        continue

                    # 执行智能节点替换
                    old_fitness = cached_edv(G, X[i], p)
                    old_individual = X[i].copy()  # 保存替换前的个体
                    # intelligent_node_replacement已经返回计算好的适应度，无需重复计算
                    new_individual, num_replaced, new_fitness = intelligent_node_replacement(G, X[i], best_solution, p, neighbors_cache=neighbors_cache)

                    # 更新节点替换统计
                    replacement_stats["total_attempts"] += 1
                    if new_fitness > old_fitness:
                        replacement_stats["successful_attempts"] += 1

                    # 检查是否超过全局最优解
                    if new_fitness > best_fitness:
                        replacement_stats["exceeded_global_best"] += 1

                    X[i] = new_individual
                    current_fitness[i] = new_fitness  # 更新当前适应度
                    stagnation_counter[i] = 0  # 重置停滞计数器

                    # 记录替换信息
                    replacement_info = { 'iteration': iter+1, 'individual_idx': i, 'old_individual': old_individual,
                        'new_individual': new_individual, 'old_fitness': old_fitness, 'new_fitness': new_fitness,
                        'actual_new_fitness': new_fitness, 'num_replaced': num_replaced, 'is_best_in_generation': False
                    }
                    replacement_data.append(replacement_info)
                # 删除以下注释掉的代码，确保不会意外将地形设置为peak
                # current_terrain = "peak"

        else:
            # print(f"第{iter+1}代 | 正常进化 (峰度值 {kurtosis_value:.4f} < {kurtosis_threshold:.4f})")
            # 峰度值低于阈值时，始终使用ridge地形
            current_terrain = "ridge"

        # 根据当前地形执行差分变异，传入邻居缓存
        M = Differential_Mutation(G, X, F, n, k, nodelist, p=p, terrain=current_terrain, neighbors_cache=neighbors_cache)
        C = Crossover(X, M, cr, n, k, nodelist, neighbors_cache=neighbors_cache)
        X = Selection(G, X, C, n, p, neighbors_cache=neighbors_cache)

        # 评估当前种群，传入邻居缓存
        current_fitness = [cached_edv(G, ind, p, neighbors_cache=neighbors_cache) for ind in X]
        current_best = max(current_fitness)
        best_idx = current_fitness.index(current_best)

        # 更新全局最优解
        if current_best > best_fitness:
            best_fitness = current_best
            best_solution = X[best_idx].copy()

        # 更新适应度历史
        fitness_history.append(current_best)
        # 更新每个个体的适应度历史并检查停滞
        recovered_count = 0
        # 找出当前代的最优个体索引
        current_best_idx = np.argmax(current_fitness)

        # 更新每个个体的适应度历史并检查停滞
        for i in range(n):
            # 判断当前个体是否为本代最优
            is_best_in_generation = (i == current_best_idx)
            recovered, replaced, new_individual, replacement_info, new_fitness = update_stagnation_detection(
                i, iter+1, individual_fitness_history, current_fitness[i],
                stagnation_counter, stagnation_detected, X, best_solution, G, p,
                stagnation_threshold=1e-2, stagnation_trigger=3, #停滞阈值设为1e-2
                is_generation_best=is_best_in_generation,  # 传入是否为当前代最优
                neighbors_cache=neighbors_cache  # 传入邻居缓存
            )

            # 如果进行了节点替换，更新个体和适应度
            if replaced:
                old_fitness = current_fitness[i]
                X[i] = new_individual
                # 使用函数返回的适应度，不再重新计算
                current_fitness[i] = new_fitness

                # 更新节点替换统计
                replacement_stats["total_attempts"] += 1
                if new_fitness > old_fitness:
                    replacement_stats["successful_attempts"] += 1

                # 检查是否超过全局最优解
                if new_fitness > best_fitness:
                    replacement_stats["exceeded_global_best"] += 1

                if replacement_info:
                    replacement_info['actual_new_fitness'] = new_fitness  # 使用新计算的适应度
                    replacement_info['is_best_in_generation'] = is_best_in_generation
                    # 检查是否超过全局最优解
                    replacement_info['exceeded_global_best'] = new_fitness > best_fitness
                    replacement_data.append(replacement_info)

            if recovered:
                recovered_count += 1


        # 更新全局最优解(如果有改进)
        generation_best_idx = np.argmax(current_fitness)
        generation_best = current_fitness[generation_best_idx]

        if generation_best > current_best:
            current_best = generation_best
            best_solution = X[generation_best_idx].copy()

        # 在所有替换和进化操作完成后，再收集当前代的数据
        current_iteration_data = {
            'individuals': X.copy(),
            'fitness': current_fitness.copy(),  # 确保使用最新的适应度值
            'terrain': current_terrain
        }
        evolution_data.append(current_iteration_data)

    #     # 输出每代个体信息
    #     for i in range(n):
    #         print(f"第{iter+1}代 | 个体{i} | 适应度: {current_fitness[i]:.4f}")

    #     print(f"当前最佳适应度: {current_best:.4f}, 平均适应度: {np.mean(current_fitness):.4f}")
    #     print(f"当前最佳适应度: {current_best:.4f}")
    #     print(f"最优个体: {best_solution}")
    #     print('=============================================================')

    # # 计算局部搜索成功率
    # local_search_success_rate = 0
    # if local_search_stats["total_attempts"] > 0:
    #     local_search_success_rate = local_search_stats["successful_attempts"] / local_search_stats["total_attempts"] * 100

    # # 计算节点替换成功率
    # replacement_success_rate = 0
    # if replacement_stats["total_attempts"] > 0:
    #     replacement_success_rate = replacement_stats["successful_attempts"] / replacement_stats["total_attempts"] * 100

    # # 输出局部搜索统计
    # print(f"==================局部搜索统计 ==================")
    # print(f"总尝试次数: {local_search_stats['total_attempts']}，成功次数: {local_search_stats['successful_attempts']}")
    # print(f"成功率: {local_search_success_rate:.2f}%")

    # # 输出节点替换统计
    # print(f"==================节点替换统计==================")
    # print(f"总尝试次数: {replacement_stats['total_attempts']}，成功次数: {replacement_stats['successful_attempts']}")
    # print(f"成功率: {replacement_success_rate:.2f}%")
    # print(f"超过全局最优次数: {replacement_stats['exceeded_global_best']}")

    # # 输出地形转换统计
    # print(f"==================地形转换统计==================")
    # print(f"切换为山峰区次数: {terrain_transitions['to_peak']}次")

    # # 输出峰度统计摘要
    # kurtosis_threshold_count = sum(1 for _, kurt in kurtosis_stats if kurt > kurtosis_threshold)
    # print(f"==================峰度统计摘要==================")
    # print(f"峰度大于{kurtosis_threshold:.0f}的迭代次数: {kurtosis_threshold_count}/{max_iter} ({kurtosis_threshold_count/max_iter*100:.2f}%)")

    # # 输出缓存统计
    # from flbasefun import cache_stats
    # print(f"==================缓存统计==================")
    # total_calls = cache_stats['total_calls']
    # hits = cache_stats['hits']
    # misses = cache_stats['misses']
    # hit_rate = (hits / total_calls * 100) if total_calls > 0 else 0
    # print(f"EDV缓存: 总调用次数: {total_calls}, 命中: {hits}, 未命中: {misses}")
    # print(f"缓存命中率: {hit_rate:.2f}%")
    # print(f"EDV缓存大小: {len(edv_cache)}项")
    # print(f"节点评分缓存大小: {len(node_score_cache)}项")

    return best_solution, fitness_history, evolution_data, replacement_data, kurtosis_stats, current_best

def main():

    # start_time = time.time()

    # 重置全局缓存
    global edv_cache, node_score_cache
    edv_cache = {}
    node_score_cache = {}

    # network_path = "D:\\VS\\code\\networks\\netscience.txt"
    # network_path = "D:\\VS\\code\\networks\\AS733.txt"
    # network_path = "D:\\VS\\code\\networks\\blog.txt"
    # network_path = "D:\\VS\\code\\networks\\pgp.txt"
    # network_path = "D:\\VS\\code\\networks\\CA-GrQc.txt"

    # network_path = "D:\\VS\\code\\networks\\CA-HepTh.txt"
    network_path = "D:\\VS\\code\\networks\\NetHEHT.txt"
    # network_path = "D:\\VS\\code\\networks\\deezer.txt"
    # network_path = "D:\\VS\\code\\networks\\twin_zero_indexed.txt"

    # network_path = "D:\\VS\\code\\networks\\WS.txt"
    # network_path = "D:\\VS\\code\\networks\\BA.txt"
    # network_path = "D:\\VS\\code\\networks\\ER.txt"


    # 提取网络名称
    network_name = network_path.split('\\')[-1].split('.')[0]
    
    # 创建结果文件
    result_filename = f"{network_name}_results.txt"
    with open(result_filename, 'w', encoding='utf-8') as f:
        f.write(f"==================实验结果汇总==================\n")
        f.write(f"Network: {network_path}\n\n")

    # 使用GEN_GRAPH类替代gen_graph函数
    graph_generator = GEN_GRAPH(network_path)
    G = graph_generator.nx_G  # 获取NetworkX图对象
    neighbors_cache = graph_generator.neighbors  # 获取邻居缓存

    p = 0.05
    # k = 50 # 种子集大小
    NP = 10 # 种群大小
    kurtosis_threshold = 3
    max_iter = 50  # 最大迭代次数

    # 设置差分进化算法参数
    F = 0.6  # 变异因子
    cr = 0.4  # 交叉概率

    # m = [10,20,30,40,50,60,70,80,90,100]
    m = [50]

    for k in m:
        
        start_time = time.time()

        # 修改DE函数调用，传入计算的峰度阈值和邻居缓存
        best_seed, fitness_history, evolution_data, replacement_data, kurtosis_stats, current_best = (
            de(G, n=NP, k=k, p=p, max_iter=max_iter, F=F, cr=cr,
            kurtosis_threshold=kurtosis_threshold, neighbors_cache=neighbors_cache))

        # 导出节点替换数据到Excel
        # excel_file = export_replacement_data_to_excel(replacement_data, network_path, k, p, kurtosis_stats)

        # 导出进化统计数据到Excel - 现在调用output模块中的函数
        # stats_excel_file = export_evolution_stats_to_excel(evolution_data, kurtosis_stats, replacement_data, network_path, k, p, max_iter)

        # 评估最终结果
        ic_value = IC(G, best_seed, p, mc=1000)
        print(f"==================最终结果==================")
        print(f"Network: {network_path}")  # 直接输出网络文件路径
        # print(f"Nodes:{G.number_of_nodes()}, Edges:{G.number_of_edges()}")
        print(f"N/E={G.number_of_nodes()}/{G.number_of_edges()}")
        print(f"k={k}, p={p}, NP={NP}")
        print(f"最佳适应度EDV={current_best:.4f}")
        print(f"seed set (size: {len(set(best_seed))}):\n {best_seed}")
        print(f"IC influence: {ic_value:.4f}")
        print(f"running time: {time.time() - start_time:.2f} s")

        # 将结果追加到同一个txt文件
        with open(result_filename, 'a', encoding='utf-8') as f:
            f.write(f"==================k={k}的结果==================\n")
            f.write(f"N/E={G.number_of_nodes()}/{G.number_of_edges()}\n")
            f.write(f"k={k}, p={p}, NP={NP}\n")
            f.write(f"最佳适应度EDV={current_best:.4f}\n")
            f.write(f"seed set (size: {len(set(best_seed))}):\n {best_seed}\n")
            f.write(f"IC influence: {ic_value:.4f}\n")
            f.write(f"running time: {time.time() - start_time:.2f} s\n\n")
        
        # print(f"结果已追加到文件: {result_filename}")

        #可视化种子集分布
        # visualize_seed_set(G, best_seed, 'seed_set_distribution.png')
        # print(f"seed_set_distribution.png saved")

        # 使用新的绘图函数，将适应度历史和峰度值变化合并到一个图表中
        if k == 50 or k == 100:
            plot_fitness_and_kurtosis(fitness_history, kurtosis_stats, f'{network_name}_k{k}_fitness_and_kurtosis.png', kurtosis_threshold=kurtosis_threshold)
            print(f"适应度和峰度图已保存到: {network_name}_k{k}_fitness_and_kurtosis.png")

        # 清理内存
        del fitness_history, evolution_data, replacement_data, kurtosis_stats
        import gc
        gc.collect()  # 强制垃圾回收

    print(f"\n所有结果已保存到文件: {result_filename}")

if __name__ == "__main__":
    main()







