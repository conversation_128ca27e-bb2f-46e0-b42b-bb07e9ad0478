import numpy as np
import networkx as nx
import random
import matplotlib.pyplot as plt
import math
import time
from collections import defaultdict
from scipy import stats
from base_fun import IC, gen_graph, EDV, degree_initialization, local_search, visualize_seed_set, plot_fitness_history

# 全局缓存，避免重复计算EDV和节点评分
edv_cache = {}
node_score_cache = {}  # 添加全局节点评分缓存

# 缓存统计
cache_stats = {
    'hits': 0,
    'misses': 0,
    'total_calls': 0,
    'last_prune_time': time.time()
}

# 优化的缓存版本的EDV，避免重复计算
def cached_edv(G, seed_set, p, k=None):
    """
    缓存版本的EDV，避免重复计算
    使用LRU策略管理缓存，并提供缓存命中统计
    """
    global edv_cache, cache_stats

    # 更新缓存统计
    cache_stats['total_calls'] += 1

    # 创建缓存键
    key = (frozenset(seed_set), p)

    # 检查缓存
    if key in edv_cache:
        cache_stats['hits'] += 1
        return edv_cache[key]

    # 缓存未命中，计算EDV
    cache_stats['misses'] += 1
    result = EDV(G, seed_set, p)
    edv_cache[key] = result

    # 自动清理缓存（每10000次调用或每60秒检查一次）
    if (cache_stats['total_calls'] % 10000 == 0 or
        time.time() - cache_stats['last_prune_time'] > 60):
        prune_cache()
        cache_stats['last_prune_time'] = time.time()

    return result

# 初始化所有节点的评分
def initialize_node_scores(G, p):
    """
    计算并缓存所有节点的度中心性评分
    使用批处理方式提高效率
    """
    global node_score_cache

    print("正在初始化所有节点度中心性评分...", end="")
    start_time = time.time()

    # 使用NetworkX的度中心性计算函数
    degree_scores = nx.degree_centrality(G)

    # 更新全局缓存
    node_score_cache.update(degree_scores)

    # 计算单节点EDV作为备用评分
    nodes = list(G.nodes())
    batch_size = 100  # 批处理大小

    print(f" 完成度中心性计算，共{len(degree_scores)}个节点")
    print(f"正在计算单节点EDV评分...", end="")

    # 批量计算单节点EDV
    for i in range(0, len(nodes), batch_size):
        batch = nodes[i:i+batch_size]
        for node in batch:
            if node not in node_score_cache:
                # 使用单节点EDV作为评分
                node_score_cache[node] = EDV(G, [node], p)

    print(f" 完成，耗时: {time.time() - start_time:.2f}秒")
    print(f"节点评分缓存大小: {len(node_score_cache)}")

# 计算和缓存单个节点的评分
def get_node_score(G, node, p):
    """
    获取单个节点的评分，使用全局缓存
    如果缓存中没有，则计算并缓存
    """
    global node_score_cache

    if node not in node_score_cache:
        # 首先尝试使用度中心性
        try:
            node_score_cache[node] = nx.degree_centrality(G)[node]
        except:
            # 如果失败，使用EDV
            node_score_cache[node] = EDV(G, [node], p)

    return node_score_cache[node]

# 添加缓存清理函数，避免内存占用过大
def prune_cache(max_size=10000):
    """
    当缓存过大时清理不常用的项
    使用LRU策略保留最近使用的项
    """
    global edv_cache, node_score_cache, cache_stats

    # 清理EDV缓存
    if len(edv_cache) > max_size:
        print(f"清理EDV缓存: {len(edv_cache)} -> {max_size//2}项")

        # 只保留最近使用的一半缓存项
        items = list(edv_cache.items())
        edv_cache = dict(items[-max_size//2:])

        # 重置缓存统计
        cache_stats['hits'] = 0
        cache_stats['misses'] = 0

    # 节点评分缓存通常较小，不需要清理
    # 但如果特别大，也可以考虑清理
    if len(node_score_cache) > max_size * 0.2:  # 如果超过阈值的20%
        print(f"警告: 节点评分缓存已增长到 {len(node_score_cache)} 项")

        # 保留所有节点的度中心性评分，但清理EDV评分
        # 这里不实际清理，只发出警告

# 计算两个解之间的汉明距离（用于度量解的差异性）
def calculate_distance(sol1, sol2):
    """计算两个解之间的汉明距离"""
    return len(set(sol1).symmetric_difference(set(sol2)))

# # 个体停滞时的智能替换策略
# def intelligent_node_replacement(G, individual, best_solution, p):
#     """
#     对陷入停滞状态的个体进行智能节点替换，并对新添加的节点进行一阶邻域搜索优化

#     返回:
#     new_individual: 替换后的新个体
#     m: 替换的节点数量
#     baseline_fitness: 新个体的适应度值
#     """
#     global node_score_cache, edv_cache

#     ind_set = set(individual)
#     best_set = set(best_solution)
#     hamming_distance = len(ind_set.symmetric_difference(best_set)) / 2 #对称差集的一半为单个集合差异节点个数
#     # print(f"ind_set: {ind_set}, \n best_set: {best_set}, \n hamming_distance: {hamming_distance}")
#     # 确保替换汉明距离大小个节点，但至少替换1个，避免无效替换
#     m = max(1, int(hamming_distance))
#     k = len(best_set)
#     if m == k:
#         m = k - 1 #最多替换k-1个节点，避免替换为度中心性

#     # 创建新个体的副本
#     new_individual = individual.copy()

#     # 找出个体中贡献度最低的m个节点
#     node_contributions = []
#     for node in individual:
#         # 计算节点贡献度 - 通过移除该节点观察影响
#         temp_set = ind_set - {node}
#         if temp_set:  # 确保不是空集
#             removed_fitness = cached_edv(G, list(temp_set), p)
#             current_fitness = cached_edv(G, list(ind_set), p)
#             contribution = current_fitness - removed_fitness
#             node_contributions.append((node, contribution))
#         else:
#             # 如果只有一个节点，则使用节点自身评分
#             node_contributions.append((node, node_score_cache.get(node, 0)))

#     # # 直接使用节点评分作为贡献度
#     # for node in individual:
#     #     node_contributions.append((node, node_score_cache.get(node, 0)))

#     # 按贡献度从低到高排序
#     node_contributions.sort(key=lambda x: x[1])
#     lowest_contributing_nodes = [node for node, _ in node_contributions[:m]]


#     # 找出不在个体中评分最高的节点
#     candidates = [(node, node_score_cache.get(node, 0))
#                  for node in G.nodes() if node not in ind_set]
#     candidates.sort(key=lambda x: x[1], reverse=True)  # 按评分从高到低排序
#     highest_scoring_candidates = [node for node, _ in candidates[:m]]

#     # 替换节点并跟踪新添加的节点
#     newly_added_nodes = []
#     actual_replaced_count = 0
#     for i in range(min(len(lowest_contributing_nodes), len(highest_scoring_candidates))):
#         remove_node = lowest_contributing_nodes[i]
#         add_node = highest_scoring_candidates[i]

#         # 在新个体中进行替换
#         new_individual.remove(remove_node)
#         new_individual.append(add_node)
#         newly_added_nodes.append(add_node)
#         actual_replaced_count += 1

#     # 计算替换后的基准适应度
#     baseline_fitness = cached_edv(G, new_individual, p)
#     new_individual_set = set(new_individual)

#     # 二次方向微调，对于新加入的节点探索一阶邻居
#     for added_node in newly_added_nodes:
#         # 获取该节点的一阶邻居
#         neighbors = list(G.neighbors(added_node))
#         valid_neighbors = [n for n in neighbors if n not in new_individual_set]

#         if not valid_neighbors:
#             continue  # 如果没有有效邻居，继续下一个节点

#         # 按节点评分排序邻居节点
#         valid_neighbors.sort(key=lambda n: node_score_cache.get(n, 0), reverse=True)

#         # 尝试用所有邻居替换当前新增节点
#         for neighbor in valid_neighbors:
#             # 创建临时解
#             temp_solution = new_individual.copy()
#             temp_solution.remove(added_node)
#             temp_solution.append(neighbor)

#             # 计算新解的适应度
#             temp_fitness = cached_edv(G, temp_solution, p)

#             # 如果找到更好的解，更新
#             if temp_fitness > baseline_fitness:
#                 new_individual = temp_solution
#                 new_individual_set = set(new_individual)
#                 baseline_fitness = temp_fitness
#                 # print("二次微调成功")
#                 break  # 找到改进就立即应用并继续下一个节点

#     return new_individual, actual_replaced_count, baseline_fitness

# 个体停滞时的智能替换策略
def intelligent_node_replacement(G, individual, best_solution, p, neighbors_cache=None):
    """
    对陷入停滞状态的个体进行智能节点替换，并对新添加的节点进行一阶邻域搜索优化

    参数:
    G: NetworkX图对象
    individual: 当前个体
    best_solution: 全局最优解
    p: 传播概率
    neighbors_cache: 节点邻居缓存字典 {节点: [邻居列表]}

    返回:
    new_individual: 替换后的新个体
    m: 替换的节点数量
    baseline_fitness: 新个体的适应度值
    """
    global node_score_cache, edv_cache

    ind_set = set(individual)
    best_set = set(best_solution)

    # 计算差异节点
    diff_nodes = ind_set.symmetric_difference(best_set)
    # 对称差集的一半为单个集合差异节点个数
    hamming_distance = len(diff_nodes) / 2

    # 确保替换汉明距离大小个节点，但至少替换1个，避免无效替换
    m = max(1, int(hamming_distance))
    k = len(best_set)
    if m == k:
        m = k - 1  # 最多替换k-1个节点，避免替换为度中心性

    # 创建新个体的副本
    new_individual = individual.copy()

    # 预计算当前适应度
    current_fitness = cached_edv(G, list(ind_set), p)

    # 优化：找出个体中贡献度最低的m个节点
    node_contributions = []

    # 优化：对于与最优解相同的节点，直接赋予高贡献度，避免计算
    common_nodes = ind_set.intersection(best_set)
    diff_ind_nodes = ind_set - best_set

    # 对于与最优解相同的节点，直接赋予高贡献度
    for node in common_nodes:
        if node in individual:  # 确保节点在列表中
            node_contributions.append((node, float('inf')))  # 使用无穷大表示高贡献度

    # 只对不同的节点计算贡献度
    for node in diff_ind_nodes:
        if node in individual:  # 确保节点在列表中
            # 计算节点贡献度 - 通过移除该节点观察影响
            temp_set = ind_set - {node}
            if temp_set:  # 确保不是空集
                removed_fitness = cached_edv(G, list(temp_set), p)
                contribution = current_fitness - removed_fitness
                node_contributions.append((node, contribution))
            else:
                # 如果只有一个节点，则使用节点自身评分
                node_contributions.append((node, node_score_cache.get(node, 0)))

    # 使用堆结构优化排序 O(n) -> O(n logm)
    import heapq
    # 按贡献度从低到高获取前m个节点
    lowest_contributing = heapq.nsmallest(m, node_contributions, key=lambda x: x[1])
    lowest_contributing_nodes = [node for node, _ in lowest_contributing]

    # 优化：预先计算候选节点集合
    candidate_nodes = list(G.nodes() - ind_set)

    # 优化：使用缓存的节点评分
    candidates = []
    for node in candidate_nodes:
        # 如果节点在最优解中但不在当前个体中，给予更高的优先级
        if node in best_set:
            # 使用一个很大的值作为优先级，确保这些节点被优先选择
            candidates.append((node, float('inf')))
        else:
            # 否则使用正常的节点评分
            candidates.append((node, node_score_cache.get(node, 0)))

    # 按评分从高到低排序
    highest_scoring = heapq.nlargest(m, candidates, key=lambda x: x[1])
    highest_scoring_candidates = [node for node, _ in highest_scoring]

    # 使用zip避免索引遍历
    newly_added_nodes = []
    actual_replaced_count = 0
    for remove_node, add_node in zip(lowest_contributing_nodes, highest_scoring_candidates):
        # 在新个体中进行替换
        new_individual.remove(remove_node)
        new_individual.append(add_node)
        newly_added_nodes.append(add_node)
        actual_replaced_count += 1

    # 计算替换后的基准适应度
    baseline_fitness = cached_edv(G, new_individual, p)
    new_individual_set = set(new_individual)

    # 个体节点替换信息
    fitness_change = baseline_fitness - current_fitness
    print(f"替换{actual_replaced_count}个节点,替换后适应度变化{fitness_change:.4f} ({(fitness_change/current_fitness*100):.2f}%)")

    # 优化：只对新添加的节点进行二次微调
    # 二次方向微调，对于新加入的节点探索一阶邻居
    for added_node in newly_added_nodes:
        # 获取该节点的一阶邻居，优先使用缓存
        if neighbors_cache and added_node in neighbors_cache:
            # 使用缓存的邻居列表
            neighbors = neighbors_cache[added_node]
        else:
            # 如果没有缓存，则通过图对象获取
            neighbors = list(G.neighbors(added_node))

        valid_neighbors = [n for n in neighbors if n not in new_individual_set]

        if not valid_neighbors:
            continue  # 如果没有有效邻居，继续下一个节点

        # 按节点评分排序邻居节点
        valid_neighbors.sort(key=lambda n: node_score_cache.get(n, 0), reverse=True)

        # 尝试用所有邻居替换当前新增节点
        for neighbor in valid_neighbors:
            # 创建临时解（直接操作列表比复制更高效）
            try:
                replace_idx = new_individual.index(added_node)
                temp_solution = new_individual[:replace_idx] + [neighbor] + new_individual[replace_idx+1:]

                # 计算新解的适应度
                temp_fitness = cached_edv(G, temp_solution, p)

                if temp_fitness > baseline_fitness:
                    # 先计算提升值，再更新基准适应度
                    improvement = temp_fitness - baseline_fitness
                    new_individual = temp_solution
                    new_individual_set = set(new_individual)  # 更新集合
                    baseline_fitness = temp_fitness
                    print(f"二次微调成功，适应度提升: {improvement:.4f}")
                    break  # 找到改进就立即应用并继续下一个节点
            except ValueError:
                continue

    return new_individual, actual_replaced_count, baseline_fitness

# 更新个体停滞检测状态，并在检测到停滞时进行智能节点替换
def update_stagnation_detection(i, current_iter, fitness_history, fitness_current,
                              stagnation_counters, stagnation_states, individuals, best_solution, G, p,
                              stagnation_threshold=0, stagnation_trigger=3, is_generation_best=False, neighbors_cache=None):
    """
    更新个体停滞检测状态，并在检测到停滞时进行智能节点替换

    返回: (是否从停滞状态中恢复, 是否进行了节点替换, 新个体, 替换信息, 新适应度)
    """
    # 更新适应度历史记录(保持最近10代)
    fitness_history[i].append(fitness_current)
    if len(fitness_history[i]) > 10:
        fitness_history[i] = fitness_history[i][-10:]

    recovered = False
    replaced = False
    new_individual = individuals[i].copy()
    replacement_info = None
    new_fitness = fitness_current  # 默认使用当前适应度

    # 至少需要两代才能计算变化
    if len(fitness_history[i]) < 2:
        return recovered, replaced, new_individual, replacement_info, new_fitness

    # 计算适应度变化
    fitness_change = fitness_history[i][-1] - fitness_history[i][-2]
    abs_change = abs(fitness_change)

    # 添加调试输出
    # if abs_change < stagnation_threshold:
    #     print(f"代 {current_iter} | 个体 {i} | 适应度变化 {abs_change:.2e} < 阈值 {stagnation_threshold} → 触发停滞检测")
    # else:
    #     print(f"代 {current_iter} | 个体 {i} | 适应度变化 {abs_change:.2e} ≥ 阈值 {stagnation_threshold} → 正常进化")

    # 情况1: 适应度显著提升 - 重置停滞状态
    if fitness_change > stagnation_threshold:
        if stagnation_states[i]:  # 如果之前处于停滞状态
            recovered = True
        stagnation_counters[i] = 0
        stagnation_states[i] = False

    # 情况2: 适应度停止变化,可能处于停滞状态
    elif abs_change < stagnation_threshold:
        stagnation_counters[i] += 1

        # 连续3代无显著变化，判定为停滞
        if stagnation_counters[i] >= stagnation_trigger:
            # 更新停滞状态标记
            stagnation_states[i] = True

            print(f"代 {current_iter} | 个体 {i} | 适应度变化 {abs_change:.2e} < 阈值 {stagnation_threshold} → 触发停滞检测")
            # 对非最优个体执行替换操作
            if not is_generation_best:
                old_individual = individuals[i].copy()
                old_fitness = fitness_current
                # intelligent_node_replacement已经返回计算好的适应度，无需重复计算
                new_individual, num_replaced, new_fitness = intelligent_node_replacement(G, individuals[i], best_solution, p, neighbors_cache=neighbors_cache)

                replaced = True
                stagnation_counters[i] = 0

                # 记录替换信息
                replacement_info = {
                    'iteration': current_iter,
                    'individual_idx': i,
                    'old_individual': old_individual,
                    'new_individual': new_individual,
                    'old_fitness': old_fitness,
                    'new_fitness': new_fitness,
                    'num_replaced': num_replaced
                }

    return recovered, replaced, new_individual, replacement_info, new_fitness

# 修改后的局部搜索函数，不再限制评估的候选节点数量
def optimized_local_search(xi, G, p, k, max_iterations=1, neighbors_cache=None):
    """
    优化版本的局部搜索，使用高效的串行+批处理策略

    参数:
    xi: 当前解
    G: NetworkX图对象
    p: 传播概率
    k: 种子集大小
    max_iterations: 最大迭代次数
    neighbors_cache: 节点邻居缓存字典 {节点: [邻居列表]}

    返回:
    优化后的解
    """
    global node_score_cache, edv_cache

    # 转换为集合和列表，避免重复转换
    xi_set = set(xi)
    xi_list = list(xi)
    xi_fitness = cached_edv(G, xi_list, p)

    improved = True
    iteration = 0

    # 使用传入的邻居缓存或创建新的缓存
    if neighbors_cache:
        # 使用传入的邻居缓存
        neighbor_cache = neighbors_cache
    else:
        # 预计算所有节点的邻居并缓存 - 只在第一次调用时计算
        if not hasattr(optimized_local_search, 'neighbor_cache'):
            print("初始化邻居缓存...")
            optimized_local_search.neighbor_cache = {}
            for node in G.nodes():
                optimized_local_search.neighbor_cache[node] = list(G.neighbors(node))

        neighbor_cache = optimized_local_search.neighbor_cache

    # 预计算所有节点的贡献度
    print("计算节点贡献度...")
    node_contributions = []

    # 优化：计算每个节点的实际贡献度，而不是简单使用节点评分
    for idx, node in enumerate(xi_list):
        # 计算移除该节点后的适应度
        temp_set = xi_set - {node}
        if temp_set:  # 确保不是空集
            removed_fitness = cached_edv(G, list(temp_set), p)
            contribution = xi_fitness - removed_fitness  # 贡献度 = 当前适应度 - 移除后适应度
            node_contributions.append((idx, node, contribution))
        else:
            # 如果只有一个节点，则使用节点自身评分
            node_contributions.append((idx, node, node_score_cache.get(node, 0)))

    # 按贡献度从低到高排序
    node_contributions.sort(key=lambda x: x[2])

    # 优化：预先计算所有可能的候选节点
    all_nodes = set(G.nodes())
    available_nodes = all_nodes - xi_set

    # 从贡献最小的节点开始尝试替换
    for idx, current_node, _ in node_contributions:
        # 获取当前节点的邻居
        neighbors = neighbor_cache.get(current_node, [])
        valid_neighbors = [n for n in neighbors if n in available_nodes]

        # 如果没有有效邻居，继续下一个节点
        if not valid_neighbors:
            continue

        # 按节点评分排序候选节点
        valid_neighbors.sort(key=lambda node: node_score_cache.get(node, 0), reverse=True)

        # 批量评估前10个最有希望的候选节点
        top_candidates = valid_neighbors[:min(10, len(valid_neighbors))]

        # 评估这些候选节点
        best_candidate = None
        best_improvement = 0
        best_new_fitness = 0

        for candidate in top_candidates:
            # 创建包含交换后节点的新种子集 - 使用列表切片避免完整复制
            new_xi = xi_list.copy()
            new_xi[idx] = candidate
            new_fitness = cached_edv(G, new_xi, p)
            improvement = new_fitness - xi_fitness

            if improvement > best_improvement:
                best_improvement = improvement
                best_candidate = candidate
                best_new_fitness = new_fitness

        # 如果找到改进，应用它
        if best_improvement > 0:
            improvement_percent = (best_improvement / xi_fitness * 100) if xi_fitness > 0 else 0
            print(f"局部搜索找到改进: {best_improvement:.4f} ({improvement_percent:.2f}%)")
            # 更新当前解
            xi_set.remove(current_node)
            xi_set.add(best_candidate)
            xi_list[idx] = best_candidate
            xi_fitness = best_new_fitness

            # 更新缓存
            key = (frozenset(xi_list), p)
            edv_cache[key] = xi_fitness

            # 立即返回改进的解
            return xi_list

    # 如果没有找到改进，返回原始解
    return xi_list



# 计算适应度序列的峰度
def calculate_kurtosis(fitness_values):
    """计算适应度序列的峰度，使用scipy.stats计算原始峰度（不减3）"""
    # 检查输入数据
    if len(fitness_values) <= 1:
        return 0

    # 检查所有值是否相同
    if len(set(fitness_values)) == 1:
        # 所有值相同时返回数据量
        return len(fitness_values)

    # 使用scipy.stats中的kurtosis函数计算原始峰度
    # fisher=False表示计算原始峰度，不减去3
    # bias=False表示使用无偏估计
    result_unbiased = stats.kurtosis(fitness_values, fisher=False, bias=False)
    return result_unbiased

# 添加采样计算峰度阈值的函数
def calculate_kurtosis_threshold(G, k, p, num_samples=10000):
    """
    通过随机采样计算峰度阈值

    参数:
    G: 图对象
    k: 种子集大小
    p: 传播概率
    num_samples: 采样数量，默认10000

    返回:
    threshold: 建议的峰度阈值
    """
    print(f"开始计算峰度阈值")
    # 节点列表
    nodelist = list(G.nodes())
    n = len(nodelist)

    # 用于存储采样解的适应度
    fitness_samples = []

    # 随机采样
    for i in range(num_samples):

        # 随机生成一个种子集
        sample = random.sample(nodelist, k)
        # 计算适应度
        fitness = EDV(G, sample, p)
        fitness_samples.append(fitness)

    # 使用scipy.stats计算采样数据的峰度
    sample_kurtosis = stats.kurtosis(fitness_samples, fisher=True) + 3  # 加3转换为总体峰度
    print(f"采样次数: {num_samples},随机采样峰度值: {sample_kurtosis:.4f}")
    return sample_kurtosis
