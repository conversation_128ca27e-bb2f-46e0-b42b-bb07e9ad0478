import numpy as np
import networkx as nx
import random
import math
import matplotlib.pyplot as plt
from multiprocessing import Pool, cpu_count
from functools import partial

"""
基于图的影响力最大化工具函数库

主要功能:
1. 图构建与操作
2. 影响力传播模拟 (IC模型)
3. 影响力评估 (EDV, LIE)
4. 种子集初始化策略
5. 优化算法 (局部搜索)
6. 可视化工具
"""

#####################################
# 1. 图构建和处理函数
#####################################

def gen_graph(filename: str) -> nx.Graph:
    """
    从文件加载图形数据并生成无向图
    
    Args:
        filename: 包含边列表的文件路径
        
    Returns:
        nx.Graph: 生成的无向图对象
        
    Raises:
        RuntimeError: 当图加载失败时
    """
    try:
        G = nx.Graph()  # 创建一个空的无向图
        edges_data = np.loadtxt(filename, skiprows=1, usecols=[0, 1])  # 读取边数据
        edges = [(int(u), int(v)) for u, v in edges_data]
        G.add_edges_from(edges)  # 将边添加到图中
        return G  # 返回生成的图
    except Exception as e:
        raise RuntimeError(f"加载图形错误: {e}")

class GEN_GRAPH:
    """Network graph generator and neighbor cache container
    
    Attributes:
        network_path (str): 网络数据文件路径（包含节点关系的文本文件）
        nx_G (nx.Graph): NetworkX图对象实例
        neighbors (dict): 节点邻居字典缓存{节点: [直接邻居列表]}
    """
    
    def __init__(self, network_path: str) -> None:
        """初始化图生成器并预生成邻居缓存
        
        Args:
            network_path: 网络数据文件路径（需符合np.loadtxt格式要求）
            
        Raises:
            FileNotFoundError: 当指定路径不存在时
            ValueError: 当文件格式不符合要求时
        """
        self.network_path = network_path
        self.nx_G = self.gen_graph()
        self.neighbors = {node: list(self.nx_G.neighbors(node)) for node in self.nx_G.nodes()}

    def gen_graph(self) -> nx.Graph:
        """从边列表文件构建无向图（支持标准边列表格式）
        
        文件格式要求：
        - 首行为表头（将被自动跳过）
        - 每行包含两个整数节点ID（从第0列和第1列读取）
        
        Returns:
            生成的NetworkX无向图对象
            
        Raises:
            FileNotFoundError: 当文件路径不存在时
            ValueError: 当数据列不足或无法转换为整数时
            RuntimeError: 当图构建过程中出现未知错误时
        """
        try:
            G = nx.Graph()
            # 使用numpy加速边数据加载（跳过首行表头）
            edges_data = np.loadtxt(
                self.network_path,
                skiprows=1,    # 忽略标题行
                usecols=[0, 1] # 仅读取前两列作为边
            )
            # 将浮点数据转换为整数节点ID（适用于非负整数ID）
            edges = [(int(u), int(v)) for u, v in edges_data]
            G.add_edges_from(edges)
            return G
        except FileNotFoundError as e:
            raise FileNotFoundError(f"Network file not found: {self.network_path}") from e
        except ValueError as e:
            raise ValueError(f"Invalid data format in {self.network_path}") from e
        except Exception as e:
            raise RuntimeError(f"Graph construction failed: {str(e)}") from e
        
#####################################
# 2. 影响力传播模拟
#####################################

def _ic_single_run(iteration, seed, g, p):
    """
    单次IC模型传播模拟
    
    Args:
        iteration: 迭代次数（由multiprocessing传入，但在函数中不使用）
        seed: 种子节点集合
        g: NetworkX图对象
        p: 传播概率
        
    Returns:
        int: 受影响节点的总数
    """
    seed = set(seed)
    new_active, last_active = set(seed), set(seed)
    while new_active:
        new_ones = set()
        for node in new_active:
            node_neighbors = list(g.neighbors(node))
            for neighbor in node_neighbors:
                if neighbor not in last_active and np.random.uniform(0, 1) < p:
                    new_ones.add(neighbor)
        new_active = new_ones - last_active
        last_active.update(new_active)
    return len(last_active)

def IC(g, seed, p, mc=1000):
    """
    并行版本的IC模型影响力传播模拟
    
    Args:
        g: NetworkX图对象
        seed: 种子节点集合
        p: 传播概率
        mc: Monte Carlo模拟次数
    
    Returns:
        float: 平均影响力大小
    """
    # 确定要使用的CPU核心数
    # num_cores = min(cpu_count(), mc)
    num_cores = 16
    
    # 创建偏函数，固定g和p参数
    ic_partial = partial(_ic_single_run, seed=seed, g=g, p=p)
    
    # 使用进程池并行执行模拟
    with Pool(num_cores) as pool:
        results = pool.map(ic_partial, range(mc))
    
    # 返回平均影响力
    return np.mean(results)

# # 影响力传播模拟 (IC模型)
# def IC(g, seed, p, mc=1000):
#     seed = set(seed)  # 转换为集合，避免重复元素
#     influence = []
#     for _ in range(mc):
#         new_active, last_active = set(seed), set(seed)  # 使用集合来去重
#         while new_active:
#             new_ones = set()
#             for node in new_active:
#                 node_neighbors = list(g.neighbors(node))  
#                 for neighbor in node_neighbors:
#                     if np.random.uniform(0, 1) < p:
#                         new_ones.add(neighbor)
#             new_active = new_ones - last_active
#             last_active.update(new_active)
#         influence.append(len(last_active))  # 记录激活的总节点数
#     return np.mean(influence)  # 返回平均影响力

#####################################
# 3. 影响力评估方法
#####################################

# def EDV(graph, S, p, cached_neighbors=None):
#     """
#     影响力评估 (Expected Diffusion Value)
    
#     Args:
#         graph: NetworkX图对象
#         S: 种子节点集合
#         p: 传播概率
#         cached_neighbors: 缓存的邻居信息(未使用)
        
#     Returns:
#         float: 估计的影响力值
#     """
#     k = len(S)
#     influence_sum = 0
#     NS_1 = set()
#     for node in S:
#         if node in graph:
#             NS_1.update(graph[node])  # 获取一阶邻居节点集合

#     for node in NS_1:
#         if node not in S:
#             num_connections = sum(1 for s in S if s in graph[node])
#             influence_sum += 1 - (1 - p) ** num_connections
#     result = (k + influence_sum)    # 计算 EDV(S)

#     return result

def EDV(graph, S, p):

    """向量化优化的EDV计算，无需显式循环"""
    # 生成节点索引映射（应对非连续节点ID）
    nodes = list(graph.nodes())
    node_index = {n: i for i, n in enumerate(nodes)}
    n = len(nodes)
    
    # 构建稀疏邻接矩阵（CSR格式加速矩阵运算）
    adj_csr = nx.adjacency_matrix(graph, nodelist=nodes).astype(bool).tocsr()
    
    # 生成种子节点掩码向量（O(1)时间访问）
    seed_indices = np.array([node_index[s] for s in S if s in node_index], dtype=np.int32)
    if len(seed_indices) == 0:
        return 0.0
    seed_mask = np.zeros(n, dtype=np.int8)
    seed_mask[seed_indices] = 1
    
    # 单次矩阵乘法获取连接数（替代双重循环）
    conn_counts = adj_csr[:, seed_indices].sum(axis=1)  # 移除 .A1
    
    # 计算一跳邻居（排除种子节点）
    one_hop_mask = (conn_counts > 0) & (seed_mask == 0)
    one_hop_counts = conn_counts[one_hop_mask]
    
    # 向量化概率计算（比循环快50倍）
    influence = np.sum(1 - np.power(1 - p, one_hop_counts))
    
    return len(seed_indices) + influence



def LIE(G, seed, p=0.1):
    """
    局部影响估计（LIE）函数，计算给定种子集合或单个节点的影响力
    
    Args:
        G: NetworkX 图对象
        seed: 种子集合（节点列表/集合）或单个节点
        p: 影响传播的概率
        
    Returns:
        float: 估计的影响力值
    """
    # 输入有效性检查
    if not G or not seed:
        return 0.0
        
    # 如果 seed 是单个节点，将其转换为集合
    if not isinstance(seed, (list, set)):
        if seed is None:
            return 0.0
        seed = {seed}
    elif isinstance(seed, list):
        seed = set(seed)
        
    # 过滤掉None节点
    seed = {node for node in seed if node is not None}
    if not seed:
        return 0.0

    k = len(seed)
    sigma_0 = k  # 初始种子集合的影响力（即种子的数量）
    sigma_1 = 0  # 一跳邻居的影响力
    sigma_2 = 0  # 二跳邻居的影响力

    # 一跳邻居
    N1 = set()
    for node in seed:
        N1.update(G.neighbors(node))

    # 计算一跳邻居的影响力
    for u in N1:
        if u not in seed:
            neighbors = list(G.neighbors(u))
            prob_sum = 1 - np.prod([(1 - p) for v in neighbors if v in seed])
            sigma_1 += prob_sum

    # 二跳邻居
    N2 = set()
    for node in N1:
        N2.update(G.neighbors(node))

    # 计算二跳邻居的影响力
    for u in N2:
        if u not in seed and u not in N1:
            neighbors = list(G.neighbors(u))
            d_u = len([v for v in neighbors if v in N1])  # 与一跳邻居的连接度
            if d_u > 0:
                prob_sum = 1 - np.prod([(1 - p) for v in neighbors if v in N1])
                sigma_2 += prob_sum * (d_u / len(N1))
    
    if len(seed) != k:
        print(f"种子集大小发生变化")

    # 返回总的影响力
    return sigma_0 + sigma_1 + sigma_2

#####################################
# 4. 种子集初始化策略
#####################################

def degree_initialization(G, n, k):
    """
    基于节点度数初始化位置
    
    Args:
        G: NetworkX 图对象
        n: 粒子数
        k: 种子集合大小
        
    Returns:
        list: 初始化的位置列表
    """
    positions = []
    for _ in range(n):
        # 基于节点度数排序后，选择k个度数最大的节点作为初始位置
        degree_sorted_nodes = sorted(G.nodes(), key=lambda node: G.degree(node), reverse=True)
        position = degree_sorted_nodes[:k]
        for i in range(k):
            if random.random() > 0.5:  # 50%概率替换
                # 获取所有不在当前解中的节点
                available_nodes = [node for node in G.nodes() if node not in position]
                if available_nodes:
                    position[i] = random.choice(available_nodes)
        positions.append(position)
    return positions

def lbc_initialization(G, n, k):
    """
    基于局部桥接中心性（LBC）初始化位置
    
    Args:
        G: NetworkX 图对象
        n: 粒子数
        k: 种子集合大小
        
    Returns:
        list: 初始化的位置列表
    """
    # 计算局部桥接中心性（LBC）
    def calculate_lbc(graph):
        lbc_scores = {}
        for node in graph.nodes():
            score = 0.0
            neighbors = list(graph.neighbors(node))
            for neighbor in neighbors:
                # 计算共同邻居数
                common_neighbors = len(list(nx.common_neighbors(graph, node, neighbor)))
                score += 1.0 / (common_neighbors + 1)
            lbc_scores[node] = score
        return lbc_scores
    
    # 获取LBC分数并排序
    lbc_scores = calculate_lbc(G)
    sorted_nodes = sorted(G.nodes(), key=lambda x: lbc_scores[x], reverse=True)
    
    positions = []
    for _ in range(n):
        # 选择LBC最高的前k个节点
        position = sorted_nodes[:k].copy()
        
        # 保持原有的随机扰动逻辑
        for i in range(k):
            if random.random() > 0.5:  # 50%概率替换
                available_nodes = [node for node in G.nodes() if node not in position]
                if available_nodes:
                    position[i] = random.choice(available_nodes)
        
        positions.append(position)
    
    return positions

#####################################
# 5. 优化算法
#####################################

def local_search(xi, G, p, k):
    """
    局部搜索算法，优化种子节点集合
    
    Args:
        xi: 当前种子集合
        G: NetworkX 图对象
        p: 传播概率
        k: 种子集合大小
        
    Returns:
        list: 优化后的种子集合
    """
    # 缓存当前种子集合的影响力值
    xi_fitness = EDV(G, xi, p, k)
    
    for x_ij in list(xi):  # 遍历当前种子节点的副本
        neighbors = list(G.neighbors(x_ij))  # 获取当前节点的邻居
        for neighbor in neighbors:  # 遍历邻居节点
            if neighbor not in xi:  # 如果邻居不在当前种子节点中
                # 尝试替换
                xi_new = xi.copy()  # 创建当前种子节点的副本
                xi_new.remove(x_ij)  # 从副本中移除当前节点
                xi_new.append(neighbor)  # 添加邻居节点
                
                # 只在影响力提高时才进行更新
                xi_new_fitness = EDV(G, xi_new, p, k)
                if xi_new_fitness > xi_fitness:  
                    xi = xi_new  # 更新种子节点
                    xi_fitness = xi_new_fitness  # 更新当前种子集合的影响力值
                    break  # 退出邻居循环，尝试对下一个种子节点优化
    
    return xi  # 返回优化后的种子节点

#####################################
# 6. 可视化工具
#####################################

def plot_fitness_history(fitness_history, save_path=None):
    """
    绘制适应度历史变化曲线
    
    Args:
        fitness_history: 适应度历史记录列表
        save_path: 保存图片的路径，默认为'fitness_history.png'
    """
    plt.figure(figsize=(10, 6), facecolor='white')
    
    # 主曲线使用更专业的蓝色调
    main_line, = plt.plot(range(len(fitness_history)), fitness_history, 
                         color='#1f77b4', linewidth=2.5, alpha=0.9)
    
    # 数据点使用更柔和的红色
    plt.scatter(range(len(fitness_history)), fitness_history, 
               color='#d62728', s=40, alpha=0.7)
    
    # 坐标轴标签和标题
    plt.xlabel('Iteration', fontsize=12, labelpad=10)
    plt.ylabel('Fitness Value', fontsize=12, labelpad=10)
    plt.title('Fitness Evolution Curve', fontsize=14, pad=15)
    
    # 网格线更精细
    plt.grid(True, linestyle=':', color='gray', alpha=0.4)
    
    # 最佳点标记
    best_idx = fitness_history.index(max(fitness_history))
    best_point = plt.scatter([best_idx], [fitness_history[best_idx]], 
                            color='#2ca02c', s=120, edgecolor='black', linewidth=1,
                            label=f'Best: {fitness_history[best_idx]:.4f} (Iter {best_idx})')
    
    # 图例和布局
    plt.legend(framealpha=0.9, loc='lower right')
    plt.tight_layout()
    
    # 保存设置
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
    else:
        plt.savefig('fitness_history.png', dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()

def visualize_seed_set(G, seed_set, save_path, layout_type='spring'):
    # 添加中文字体配置
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文字体
    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
    
    plt.figure(figsize=(15, 10))
    
    # 获取一阶邻居节点
    neighbors_1 = set()
    for node in seed_set:
        neighbors_1.update(G.neighbors(node))
    neighbors_1 = neighbors_1 - set(seed_set)  # 排除种子节点本身
    
    # 创建节点颜色映射 (种子节点:红色, 一阶邻居:黄色, 其他:蓝色)
    node_colors = ['red' if node in seed_set else 
                  'yellow' if node in neighbors_1 else 
                  'skyblue' for node in G.nodes()]
    
    # 创建节点大小映射 (种子节点:300, 一阶邻居:100, 其他:50)
    node_sizes = [300 if node in seed_set else 
                 100 if node in neighbors_1 else 
                 50 for node in G.nodes()]
    
    # 选择布局算法
    if layout_type == 'spring':
        pos = nx.spring_layout(G, k=0.15, iterations=50)  # 加大排斥力避免重叠
    elif layout_type == 'kamada_kawai':
        pos = nx.kamada_kawai_layout(G)
    elif layout_type == 'spectral':
        pos = nx.spectral_layout(G)
    else:
        pos = nx.random_layout(G)

    # 绘制网络结构
    nx.draw_networkx_nodes(G, pos, node_color=node_colors, node_size=node_sizes, alpha=0.9)
    nx.draw_networkx_edges(G, pos, width=0.5, alpha=0.2)
    
    # 突出显示种子节点标签
    seed_labels = {node: f"{node}\n(度:{G.degree(node)})" for node in seed_set}
    nx.draw_networkx_labels(G, pos, labels=seed_labels, font_size=8, font_color='darkred')
    
    # 修改标题设置方式
    plt.title(f"种子节点分布可视化（{layout_type}布局）", fontproperties='SimHei')  # 显式指定中文字体
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()