import numpy as np
import random
import math
from flbasefun import edv_cache, node_score_cache, cached_edv
from base_fun import EDV

# 优化的差分变异操作
def Differential_Mutation(G, X, F, pop, k, nodelist, p=0.1, terrain="ridge", best_solution=None, neighbors_cache=None):
    """
    基于地形特征的差分变异操作

    参数:
    G: 图对象
    X: 当前种群
    F: 缩放因子
    pop: 种群大小
    k: 种子集大小
    nodelist: 节点列表
    p: 传播概率
    terrain: 地形类型 ("ridge"或"peak")
    best_solution: 当前最优解
    neighbors_cache: 节点邻居缓存字典 {节点: [邻居列表]}

    返回:
    M: 变异后的种群
    """
    global node_score_cache

    # 优化：使用传入的邻居缓存或缓存节点列表
    if neighbors_cache:
        # 使用传入的邻居缓存
        nodes_set = set(G.nodes())
        nodes_list = list(nodes_set)
    else:
        # 缓存节点列表，避免重复创建
        if not hasattr(Differential_Mutation, 'nodes_set'):
            Differential_Mutation.nodes_set = set(G.nodes())
            Differential_Mutation.nodes_list = list(Differential_Mutation.nodes_set)

        nodes_set = Differential_Mutation.nodes_set
        nodes_list = Differential_Mutation.nodes_list

    # 预先计算所有个体的集合表示，避免重复转换
    X_sets = [frozenset(x) for x in X]

    # 预先分配结果容器
    M = [None] * pop

    # 对每个个体执行变异操作
    for i in range(pop):
        # 当前解
        Xcurrent = X[i].copy()
        current_set = set(Xcurrent)

        # === 基于地形特征的搜索策略选择 ===
        if terrain == "ridge":
            # 山脊区域使用标准DE/rand/1/bin策略
            # 使用random.sample一次性获取所有随机索引
            rand_indices = random.sample(range(pop), min(3, pop))
            base_vector = X[rand_indices[0]].copy()
            # 计算差异集合
            diff_set = X_sets[rand_indices[1]] - X_sets[rand_indices[2]]
        else:
            # 山峰区域使用DE/current-to-rand/1/bin变体
            rand_indices = random.sample(range(pop), min(3, pop))
            base_vector = Xcurrent.copy()
            # 计算差异集合
            diff_set1 = X_sets[rand_indices[0]] - X_sets[i]  # 使用预计算的集合
            diff_set2 = X_sets[rand_indices[1]] - X_sets[rand_indices[2]]
            diff_set = diff_set1.union(diff_set2)

        M[i] = base_vector

        # 计算需要替换的节点数量
        N = math.ceil(F * len(diff_set))
        if N == 0:
            continue  # 如果没有需要替换的节点，跳过当前个体

        # 优化：预先计算当前个体中所有节点的评分
        current_set = set(M[i])

        # 如果有邻居缓存，使用邻居信息增强节点评分
        if neighbors_cache:
            # 计算每个节点的邻居覆盖率（邻居中已在解中的节点比例）
            node_scores = {}
            for node in current_set:
                # 基础评分
                base_score = node_score_cache.get(node, 0)

                # 获取节点的邻居
                neighbors = neighbors_cache.get(node, [])
                if neighbors:
                    # 计算邻居中已在解中的节点数量
                    overlap = sum(1 for n in neighbors if n in current_set)
                    # 邻居覆盖率（0-1之间）
                    coverage = overlap / len(neighbors) if len(neighbors) > 0 else 0
                    # 降低高覆盖率节点的评分（这些节点可能冗余）
                    node_scores[node] = base_score * (1 - 0.5 * coverage)
                else:
                    node_scores[node] = base_score
        else:
            # 使用原始评分
            node_scores = {node: node_score_cache.get(node, 0) for node in current_set}

        # 优化：预先排序节点，找出最差的N个节点
        worst_nodes = sorted(current_set, key=lambda node: node_scores.get(node, 0))[:N]

        # 优化：预先计算可用的差异节点
        available_diff = list(diff_set - current_set)

        # 如果差异集合不足，预先计算所有可用节点
        if len(available_diff) < N:
            all_available = list(nodes_set - current_set)
            # 合并两个列表，确保差异节点优先使用
            available_nodes = available_diff + all_available
        else:
            available_nodes = available_diff

        # 如果有邻居缓存，优先选择能增加覆盖率的节点
        if neighbors_cache and available_nodes:
            # 计算每个可用节点能带来的新邻居数量
            node_potential = {}
            for node in available_nodes[:min(len(available_nodes), 100)]:  # 限制计算量
                # 获取节点的邻居
                neighbors = neighbors_cache.get(node, [])
                # 计算能带来的新邻居数量
                new_neighbors = sum(1 for n in neighbors if n not in current_set)
                # 结合节点自身评分和新邻居数量
                node_potential[node] = node_score_cache.get(node, 0) * (1 + 0.1 * new_neighbors)

            # 重新排序可用节点
            available_nodes = sorted(node_potential.keys(), key=lambda n: node_potential[n], reverse=True)

        # 执行节点替换
        for j, worst_node in enumerate(worst_nodes):
            if j >= len(available_nodes):
                break  # 如果没有足够的可用节点，提前结束

            # 选择替换节点
            replace_node = available_nodes[j]

            try:
                # 在列表中替换节点
                worst_index = M[i].index(worst_node)
                M[i][worst_index] = replace_node

                # 更新当前集合
                current_set.remove(worst_node)
                current_set.add(replace_node)
            except ValueError:
                # 如果找不到节点，重建整个列表
                M[i] = list(current_set - {worst_node} | {replace_node})

                # 确保列表长度为k
                if len(M[i]) < k:
                    remaining = k - len(M[i])
                    additional_nodes = random.sample(
                        [n for n in nodes_list if n not in M[i]],
                        min(remaining, len(nodes_list) - len(M[i]))
                    )
                    M[i].extend(additional_nodes)

                # 更新当前集合
                current_set = set(M[i])

    return M

# 优化的交叉操作
def Crossover(X, M, cr, pop, k, nodelist, neighbors_cache=None):
    """
    优化版交叉操作

    参数:
    X: 当前种群
    M: 变异后的种群
    cr: 交叉概率
    pop: 种群大小
    k: 种子集大小
    nodelist: 节点列表
    neighbors_cache: 节点邻居缓存字典 {节点: [邻居列表]}

    返回:
    real_C: 交叉后的种群
    """
    global node_score_cache

    # 优化：缓存节点集合，避免重复创建
    if not hasattr(Crossover, 'nodelist_set'):
        Crossover.nodelist_set = set(nodelist)

    nodelist_set = Crossover.nodelist_set

    # 预先分配结果容器
    real_C = [None] * pop

    # 预生成随机数列表，减少函数调用开销
    random_values = [random.random() for _ in range(pop * k)]

    # 批量处理所有个体
    for i in range(pop):
        # 创建跟踪集合和结果列表
        added_nodes = set()
        C = []

        # 优化：预先获取当前个体的X和M
        X_i = X[i]
        M_i = M[i]

        # 第一阶段：尝试按照交叉概率选择节点
        for j in range(k):
            ran = random_values[i * k + j]

            # 优化：简化逻辑判断
            if ran < cr and M_i[j] not in added_nodes:
                # 使用变异个体的节点
                temp = M_i[j]
            elif X_i[j] not in added_nodes:
                # 使用原始个体的节点
                temp = X_i[j]
            else:
                # 两个节点都已使用，暂时跳过
                continue

            C.append(temp)
            added_nodes.add(temp)

        # 第二阶段：处理冲突和填充缺失节点
        if len(C) < k:
            # 计算还需要多少个节点
            remaining = k - len(C)

            # 获取所有可用节点
            available_nodes = list(nodelist_set - added_nodes)

            # 如果有邻居缓存，使用邻居信息增强节点评分
            if neighbors_cache and len(C) > 0:
                # 计算每个可用节点与当前已选节点的连接性
                node_connectivity = {}
                current_set = set(C)

                for node in available_nodes[:min(len(available_nodes), 200)]:  # 限制计算量
                    # 基础评分
                    base_score = node_score_cache.get(node, 0)

                    # 获取节点的邻居
                    neighbors = neighbors_cache.get(node, [])
                    if neighbors:
                        # 计算与当前解的连接数
                        connections = sum(1 for n in neighbors if n in current_set)
                        # 连接密度（0-1之间）
                        connectivity = connections / len(current_set) if len(current_set) > 0 else 0
                        # 提高连接性好的节点的评分
                        node_connectivity[node] = base_score * (1 + 0.2 * connectivity)
                    else:
                        node_connectivity[node] = base_score

                # 按增强后的评分排序
                available_nodes = sorted(node_connectivity.keys(), key=lambda n: node_connectivity[n], reverse=True)
            # 如果有节点评分但没有邻居缓存，按评分排序
            elif node_score_cache:
                available_nodes.sort(key=lambda n: node_score_cache.get(n, 0), reverse=True)

            # 选择评分最高的节点填充
            best_remaining = available_nodes[:remaining]
            C.extend(best_remaining)

        # 确保长度正确
        assert len(C) == k, f"交叉后个体长度错误: {len(C)} != {k}"

        # 存储结果
        real_C[i] = C

    return real_C

# 优化的选择操作
def Selection(G, X, C, pop, p=0.1, neighbors_cache=None):
    """
    优化版选择操作

    参数:
    G: 图对象
    X: 当前种群
    C: 交叉后的种群
    pop: 种群大小
    p: 传播概率
    neighbors_cache: 节点邻居缓存字典 {节点: [邻居列表]}

    返回:
    temp_X: 选择后的新种群
    """
    global edv_cache

    # 预先分配结果容器
    temp_X = [None] * pop

    # 预先计算所有解的集合表示
    X_keys = [frozenset(x) for x in X]
    C_keys = [frozenset(c) for c in C]

    # 找出所有需要评估的唯一解
    solutions_to_evaluate = set()
    for i in range(pop):
        solutions_to_evaluate.add((X_keys[i], p))
        solutions_to_evaluate.add((C_keys[i], p))

    # 批量计算所有未缓存的解的适应度
    uncached_solutions = [(sol, list(sol[0])) for sol in solutions_to_evaluate if sol not in edv_cache]

    if uncached_solutions:
        # print(f"计算{len(uncached_solutions)}个未缓存的解...")
        for sol_key, sol_list in uncached_solutions:
            edv_cache[sol_key] = EDV(G, sol_list, p)

    # 执行选择操作
    for i in range(pop):
        x_fitness = edv_cache[(X_keys[i], p)]
        c_fitness = edv_cache[(C_keys[i], p)]

        # 贪婪选择：保留适应度更高的个体
        if x_fitness >= c_fitness:
            temp_X[i] = X[i]
        else:
            temp_X[i] = C[i]

    return temp_X
