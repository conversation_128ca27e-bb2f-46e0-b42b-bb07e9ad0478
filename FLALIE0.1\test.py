import numpy as np
import networkx as nx
import random
import matplotlib.pyplot as plt
import math
import time
import pandas as pd
import openpyxl
from collections import defaultdict
from scipy import stats
from datetime import datetime
from base_fun import IC, gen_graph, EDV, degree_initialization, local_search, visualize_seed_set, plot_fitness_history
from evolution_trajectory import plot_evolution_on_landscape
from plot import plot_fitness_and_kurtosis, plot_kurtosis
from output import export_replacement_data_to_excel, export_evolution_stats_to_excel
from fl_basefun import (edv_cache, node_score_cache, get_node_score, cached_edv,
                       initialize_node_scores, prune_cache, calculate_distance,
                       optimized_local_search, calculate_kurtosis, calculate_kurtosis_threshold,
                       intelligent_node_replacement, update_stagnation_detection)
from differential_evolution import differential_mutation, crossover, selection

# 优化的差分进化算法主函数
def de(G, n, k, p, max_iter=50, F=0.6, cr=0.4, kurtosis_threshold=3):
    """
    优化的差分进化算法主函数

    参数:
    G: 图对象
    n: 种群大小
    k: 种子集大小
    p: 传播概率
    max_iter: 最大迭代次数
    F: 变异因子
    cr: 交叉概率
    kurtosis_threshold: 峰度阈值

    返回:
    best_solution: 最优解
    fitness_history: 适应度历史
    evolution_data: 进化数据
    replacement_data: 节点替换数据
    kurtosis_stats: 峰度统计
    """
    global node_score_cache
    initialize_node_scores(G, p, method="degree")

    nodelist = list(G.nodes())
    fitness_history = []

    # 地形统计 - 只统计切换为山峰的次数
    terrain_transitions = {"to_peak": 0}
    # 局部搜索统计
    local_search_stats = {"total_attempts": 0, "successful_attempts": 0}
    # 节点替换统计
    replacement_stats = {"total_attempts": 0, "successful_attempts": 0, "exceeded_global_best": 0}

    # 修改停滞检测的初始化
    individual_fitness_history = [[] for _ in range(n)]  # 每个个体的适应度历史
    stagnation_detected = [False] * n  # 记录每个个体是否处于停滞状态
    stagnation_counter = [0] * n  # 记录每个个体连续停滞的代数

    # 峰度计算相关数据结构
    kurtosis_stats = []  # 存储每代的峰度值
    kurtosis_history = []  # 记录所有代的峰度值

    # 初始化种群
    X = degree_initialization(G, n, k)

    # 预计算初始种群适应度
    unique_solutions = {tuple(sorted(ind)) for ind in X}
    for sol in unique_solutions:
        edv_cache[sol] = EDV(G, list(sol), p)

    # 获取当前最佳解
    population_fitness = [cached_edv(G, ind, p) for ind in X]
    current_best = max(population_fitness)
    current_best_idx = population_fitness.index(current_best)

    best_solution = X[current_best_idx].copy()
    best_fitness = current_best
    fitness_history.append(best_fitness)

    # 初始化地形 - 默认设为山脊
    current_terrain = "ridge"

    # 初始化进化轨迹数据收集
    evolution_data = []
    replacement_data = []  # 新增：记录节点替换数据

    # 主进化循环
    for iter in range(max_iter):
        # 评估当前种群
        current_fitness = [cached_edv(G, ind, p) for ind in X]

        # 计算当前种群的峰度值
        result_unbiased, result_biased = calculate_kurtosis(current_fitness)
        print(f"峰度值 (unbiased): {result_unbiased:.4f}, 峰度值 (biased): {result_biased:.4f}")
        kurtosis_value = result_unbiased

        kurtosis_stats.append((iter+1, kurtosis_value))
        kurtosis_history.append(kurtosis_value)

        # 使用计算的峰度阈值替代固定值3
        if kurtosis_value > kurtosis_threshold:
            print(f"第{iter+1}代 | 激活地形感知策略 (峰度值 {kurtosis_value:.4f} > {kurtosis_threshold:.4f})")

            # 计算所有个体的适应度并找出最优个体
            individual_fitness = [(i, cached_edv(G, ind, p)) for i, ind in enumerate(X)]
            individual_fitness.sort(key=lambda x: x[1], reverse=True)
            best_individual_idx = individual_fitness[0][0]
            best_individual = X[best_individual_idx].copy()

            # 对最优个体执行局部搜索
            start_time = time.time()
            optimized = optimized_local_search(best_individual, G, p, k)
            print(f"局部搜索完成，用时: {time.time() - start_time:.2f} 秒")
            original_fitness = cached_edv(G, best_individual, p)
            new_fitness = cached_edv(G, optimized, p)

            # 更新局部搜索统计
            local_search_stats["total_attempts"] += 1

            # 根据局部搜索结果判断地形
            if new_fitness > original_fitness:
                # 局部搜索有改进，处于山峰区
                local_search_stats["successful_attempts"] += 1

                # 只有当从非山峰区切换到山峰区时才记录
                if current_terrain != "peak":
                    terrain_transitions["to_peak"] += 1
                    print(f"地形从{current_terrain}切换为peak")

                current_terrain = "peak"
                X[best_individual_idx] = optimized  # 使用优化后的个体替换
                current_fitness[best_individual_idx] = new_fitness  # 更新适应度
            else:
                print(f"^^^^^^^^^^^^^陷入局部最优峰值，执行引导策略^^^^^^^^^^^^^")
                # 无论之前是什么地形，都设置为ridge
                current_terrain = "ridge"

                # 执行引导策略,计算每个个体与最优解的汉明距离，对停滞个体执行节点替换
                for i in range(n):
                    # 跳过最优个体,只对非最优个体执行节点替换
                    if i == best_individual_idx:
                        continue

                    # 执行智能节点替换
                    old_fitness = cached_edv(G, X[i], p)
                    old_individual = X[i].copy()  # 保存替换前的个体
                    new_individual, num_replaced, new_fitness = intelligent_node_replacement(G, X[i], best_solution, p)

                    # 再次计算新个体的实际适应度，确保与缓存一致
                    new_fitness = cached_edv(G, new_individual, p)

                    # 更新节点替换统计
                    replacement_stats["total_attempts"] += 1
                    if new_fitness > old_fitness:
                        replacement_stats["successful_attempts"] += 1

                    # 检查是否超过全局最优解
                    if new_fitness > best_fitness:
                        replacement_stats["exceeded_global_best"] += 1

                    X[i] = new_individual
                    current_fitness[i] = new_fitness  # 更新当前适应度
                    stagnation_counter[i] = 0  # 重置停滞计数器

                    # 记录替换信息
                    replacement_info = { 'iteration': iter+1, 'individual_idx': i, 'old_individual': old_individual,
                        'new_individual': new_individual, 'old_fitness': old_fitness, 'new_fitness': new_fitness,
                        'actual_new_fitness': new_fitness, 'num_replaced': num_replaced, 'is_best_in_generation': False
                    }
                    replacement_data.append(replacement_info)

        else:
            print(f"第{iter+1}代 | 正常进化 (峰度值 {kurtosis_value:.4f} < {kurtosis_threshold:.4f})")
            # 峰度值低于阈值时，始终使用ridge地形
            current_terrain = "ridge"

        # 根据当前地形执行差分变异
        M = differential_mutation(G, X, F, n, k, p=p, terrain=current_terrain)
        C = crossover(X, M, cr, n, k, nodelist)
        X = selection(G, X, C, n, p)

        # 评估当前种群
        current_fitness = [cached_edv(G, ind, p) for ind in X]
        current_best = max(current_fitness)
        best_idx = current_fitness.index(current_best)

        # 更新全局最优解
        if current_best > best_fitness:
            best_fitness = current_best
            best_solution = X[best_idx].copy()

        # 更新适应度历史
        fitness_history.append(current_best)
        # 更新每个个体的适应度历史并检查停滞
        recovered_count = 0
        # 找出当前代的最优个体索引
        current_best_idx = np.argmax(current_fitness)

        # 更新每个个体的适应度历史并检查停滞
        for i in range(n):
            # 判断当前个体是否为本代最优
            is_best_in_generation = (i == current_best_idx)
            recovered, replaced, new_individual, replacement_info, new_fitness = update_stagnation_detection(
                i, iter+1, individual_fitness_history, current_fitness[i],
                stagnation_counter, stagnation_detected, X, best_solution, G, p,
                stagnation_threshold=1e-6, stagnation_trigger=3, #停滞阈值设为1e-6
                is_generation_best=is_best_in_generation  # 传入是否为当前代最优
            )

            # 如果进行了节点替换，更新个体和适应度
            if replaced:
                old_fitness = current_fitness[i]
                X[i] = new_individual
                # 使用函数返回的适应度，不再重新计算
                current_fitness[i] = new_fitness

                # 更新节点替换统计
                replacement_stats["total_attempts"] += 1
                if new_fitness > old_fitness:
                    replacement_stats["successful_attempts"] += 1

                # 检查是否超过全局最优解
                if new_fitness > best_fitness:
                    replacement_stats["exceeded_global_best"] += 1

                if replacement_info:
                    replacement_info['actual_new_fitness'] = new_fitness  # 使用新计算的适应度
                    replacement_info['is_best_in_generation'] = is_best_in_generation
                    # 检查是否超过全局最优解
                    replacement_info['exceeded_global_best'] = new_fitness > best_fitness
                    replacement_data.append(replacement_info)

            if recovered:
                recovered_count += 1


        # 更新全局最优解(如果有改进)
        generation_best_idx = np.argmax(current_fitness)
        generation_best = current_fitness[generation_best_idx]

        if generation_best > current_best:
            current_best = generation_best
            best_solution = X[generation_best_idx].copy()

        # 在所有替换和进化操作完成后，再收集当前代的数据
        current_iteration_data = {
            'individuals': X.copy(),
            'fitness': current_fitness.copy(),  # 确保使用最新的适应度值
            'terrain': current_terrain
        }
        evolution_data.append(current_iteration_data)

        print(f"当前最佳适应度: {current_best:.4f}, 平均适应度: {np.mean(current_fitness):.4f}")
        print(f"最优个体: {best_solution}")
        print('=============================================================')

    # 计算局部搜索成功率
    local_search_success_rate = 0
    if local_search_stats["total_attempts"] > 0:
        local_search_success_rate = local_search_stats["successful_attempts"] / local_search_stats["total_attempts"] * 100

    # 计算节点替换成功率
    replacement_success_rate = 0
    if replacement_stats["total_attempts"] > 0:
        replacement_success_rate = replacement_stats["successful_attempts"] / replacement_stats["total_attempts"] * 100

    # 输出局部搜索统计
    print(f"==================局部搜索统计 ==================")
    print(f"总尝试次数: {local_search_stats['total_attempts']}，成功次数: {local_search_stats['successful_attempts']}")
    print(f"成功率: {local_search_success_rate:.2f}%")

    # 输出节点替换统计
    print(f"==================节点替换统计==================")
    print(f"总尝试次数: {replacement_stats['total_attempts']}，成功次数: {replacement_stats['successful_attempts']}")
    print(f"成功率: {replacement_success_rate:.2f}%")
    print(f"超过全局最优次数: {replacement_stats['exceeded_global_best']}")

    # 输出地形转换统计
    print(f"==================地形转换统计==================")
    print(f"切换为山峰区次数: {terrain_transitions['to_peak']}次")

    # 输出峰度统计摘要
    kurtosis_threshold_count = sum(1 for _, kurt in kurtosis_stats if kurt > kurtosis_threshold)
    print(f"==================峰度统计摘要==================")
    print(f"峰度大于{kurtosis_threshold:.0f}的迭代次数: {kurtosis_threshold_count}/{max_iter} ({kurtosis_threshold_count/max_iter*100:.2f}%)")

    return best_solution, fitness_history, evolution_data, replacement_data, kurtosis_stats



def main():
    """主函数，运行差分进化算法进行影响力最大化"""
    start_time = time.time()

    # 重置全局缓存
    prune_cache(0)  # 传入0表示清空缓存

    network_path = "D:\\VS\\code\\networks\\netscience.txt"
    # network_path = "D:\\VS\\code\\networks\\email.txt"
    # network_path = "D:\\VS\\code\\networks\\blog.txt"
    # network_path = "D:\\VS\\code\\networks\\CA-HepTh.txt"
    # network_path = "D:\\VS\\code\\networks\\NetHEHT.txt"
    # network_path = "D:\\VS\\code\\networks\\deezer.txt"

    G = gen_graph(network_path)
    p = 0.05
    k = 50  # 种子集大小
    NP = 10  # 种群大小
    kurtosis_threshold = 3
    max_iter = 50  # 最大迭代次数

    # 设置差分进化算法参数
    F = 0.6  # 变异因子
    cr = 0.4  # 交叉概率

    # 使用fl_basefun.py中的de函数
    best_seed, fitness_history, evolution_data, replacement_data, kurtosis_stats = (
        de(G, n=NP, k=k, p=p, max_iter=max_iter, F=F, cr=cr, kurtosis_threshold=kurtosis_threshold))

    # 导出节点替换数据到Excel
    export_replacement_data_to_excel(replacement_data, network_path, k, p, kurtosis_stats)

    # 导出进化统计数据到Excel - 现在调用output模块中的函数
    # stats_excel_file = export_evolution_stats_to_excel(evolution_data, kurtosis_stats, replacement_data, network_path, k, p, max_iter)

    # 评估最终结果
    ic_value = IC(G, best_seed, p, mc=1000)
    print(f"==================最终结果==================")
    print(f"Network: {network_path}")  # 直接输出网络文件路径
    print(f"k={k}，p={p},NP={NP}")
    print(f"seed set (size: {len(set(best_seed))}):\n {best_seed}")
    print(f"IC influence: {ic_value:.4f}")
    print(f"running time: {time.time() - start_time:.2f} s")

    # 可视化种子集分布
    # visualize_seed_set(G, best_seed, 'seed_set_distribution.png')
    # print(f"seed_set_distribution.png saved")

    # 使用新的绘图函数，将适应度历史和峰度值变化合并到一个图表中
    plot_fitness_and_kurtosis(fitness_history, kurtosis_stats, 'fitness_and_kurtosis.png', kurtosis_threshold=kurtosis_threshold)

    # 以下注释掉原来分开的绘图函数调用
    # plot_fitness_history(fitness_history, 'fitness_history.png')
    # print(f"fitness_history.png saved")
    # plot_kurtosis(kurtosis_stats, "峰度值变化趋势", kurtosis_threshold=kurtosis_threshold)
    # print(f"峰度值变化趋势.png saved")

    # 绘制进化轨迹 - 确保传入正确的save_path参数
    # save_path = 'evolution_landscape.pdf'
    # print(f"准备保存图像到: {save_path}")
    # plot_evolution_on_landscape(evolution_data, interval=1, show_iteration_colorbar=False,
    #                            replacement_data=replacement_data, save_path=save_path)

if __name__ == "__main__":
    main()
